package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.request.ExpenseRequest;
import my.com.mandrill.component.service.ExpenseIntegrationService;
import my.com.mandrill.component.service.ExpenseService;
import my.com.mandrill.component.service.ExpenseTypeService;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "10-expense")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("expenses")
public class ExpenseController {

	private final ExpenseService expenseService;

	private final ExpenseIntegrationService expenseIntegrationService;

	private final ExpenseTypeService expenseTypeService;

	private final ObjectMapper objectMapper;

	private final PopulateService populateService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<ExpenseDTO>> findByExpenseType(@RequestParam(required = false) String expenseTypeId,
			Sort sort) {
		ExpenseType expenseType = StringUtils.isNotBlank(expenseTypeId) ? expenseTypeService.findById(expenseTypeId)
				: null;
		User user = new User(SecurityUtil.currentUserId());
		List<Expense> result = expenseService.findByUserAndExpenseType(user, expenseType, sort);
		return ResponseEntity
				.ok(result.stream().map(expense -> objectMapper.convertValue(expense, ExpenseDTO.class)).toList());
	}

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<ExpenseDTO> create(@Valid @RequestBody ExpenseRequest request) {
		Expense expense = MapStructConverter.MAPPER.toExpense(request);

		User user = new User(SecurityUtil.currentUserId());
		Expense result = expenseIntegrationService.save(expense, user);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toExpenseInternalDTO(result));
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<ExpenseDTO> update(@PathVariable String id, @Valid @RequestBody ExpenseRequest request) {
		Expense expense = MapStructConverter.MAPPER.toExpense(request);
		expense.setId(id);

		User user = new User(SecurityUtil.currentUserId());
		Expense result = expenseIntegrationService.save(expense, user);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toExpenseInternalDTO(result));
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<ExpenseDTO> show(@PathVariable String id) {
		User user = new User(SecurityUtil.currentUserId());
		Expense expense = expenseService.findByIdAndUser(id, user);

		ExpenseDTO result = MapStructConverter.MAPPER.toExpenseInternalDTO(expense);
		populateService.populateExpenseReminder(result);
		return ResponseEntity.ok(result);
	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {
		User user = new User(SecurityUtil.currentUserId());
		expenseIntegrationService.delete(id, user);
	}

	@GetMapping("/integrations/current")
	public ResponseEntity<List<ExpenseDTO>> getAllExpenses() {
		List<ExpenseDTO> result = expenseIntegrationService.getUserCurrentExpenses();
		return ResponseEntity.ok(result);
	}

}
