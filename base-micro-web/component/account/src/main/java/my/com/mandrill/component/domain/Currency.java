package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "currency", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
@JsonIdentityInfo(generator = ObjectIdGenerators.UUIDGenerator.class, scope = Currency.class)
public class Currency extends AuditSection implements Serializable {

	@NotBlank
	@Size(max = 10)
	@Column(nullable = false, length = 10)
	private String code;

	@NotBlank
	@Size(max = 50)
	@Column(nullable = false, length = 50)
	private String name;

	@Size(max = 255)
	private String description;

	@Builder.Default
	@Column(nullable = false, columnDefinition = "BOOLEAN DEFAULT TRUE")
	private boolean active = true;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		Currency currency = (Currency) o;
		return getId() != null && Objects.equals(getId(), currency.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
