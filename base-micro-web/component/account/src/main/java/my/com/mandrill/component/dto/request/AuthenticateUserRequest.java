package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;

public record AuthenticateUserRequest(@NotBlank String accessType, @NotNull LoginTypeEnum loginType,
		@NotNull PasscodeType passcodeType, @NotBlank String username, @NotBlank String password) {
}
