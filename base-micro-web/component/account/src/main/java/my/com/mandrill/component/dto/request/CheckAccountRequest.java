package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.UsernameType;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckAccountRequest {

	@NotNull
	private UsernameType usernameType;

	@NotNull
	private String username;

}
