package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.TokenTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface TokenTransactionRepository
		extends JpaRepository<TokenTransaction, String>, JpaSpecificationExecutor<TokenTransaction> {

	@EntityGraph(attributePaths = { "user", "token" })
	Page<TokenTransaction> findAll(Specification<TokenTransaction> spec, Pageable pageable);

}
