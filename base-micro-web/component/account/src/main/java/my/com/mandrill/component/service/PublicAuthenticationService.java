package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.PublicAuthentication;
import my.com.mandrill.component.dto.model.PublicAuthenticationDTO;

import java.util.List;
import java.util.Optional;

public interface PublicAuthenticationService {

	PublicAuthentication save(PublicAuthentication publicAuthentication);

	Optional<PublicAuthentication> findOptionalByIdentifier(String identifier);

	List<PublicAuthentication> findAll();

	PublicAuthentication findById(String id);

	boolean existsByIdentifier(String identifier);

	PublicAuthenticationDTO findPublicKeyByIdentifier(String identifier);

}
