<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="account-component_20230216_0001" author="muhdlaziem">
        <createTable tableName="institution">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_institution"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="short_name" type="VARCHAR(20)"/>
            <column name="business_reg_no" type="VARCHAR(50)"/>
            <column name="industry" type="VARCHAR(50)"/>
            <column name="address" type="VARCHAR(500)"/>
            <column name="phone" type="VARCHAR(20)"/>
            <column name="fax" type="VARCHAR(50)"/>
            <column name="email" type="VARCHAR(100)"/>
            <column name="website" type="VARCHAR(100)"/>
            <column defaultValueNumeric="0" name="tier" type="INT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="path" type="VARCHAR(255)"/>
            <column defaultValueBoolean="false" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="parent_id" type="VARCHAR(36)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_281318b0a27130863eb75116c" tableName="institution"/>
        <addForeignKeyConstraint baseColumnNames="parent_id" baseTableName="institution"
                                 constraintName="FK_INSTITUTION_ON_PARENT" referencedColumnNames="id"
                                 referencedTableName="institution"/>

        <createTable tableName="running_number">
            <column name="module_name" type="VARCHAR(100)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_running_number"/>
            </column>
            <column name="running_number" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="prefix" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="%d" name="number_format" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="module_name, institution_id" constraintName="uc_beb280372e3203dd5dbc5fb22"
                             tableName="running_number"/>

        <createTable tableName="app_permission">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_permission"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="category" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="admin" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="name, code, category" constraintName="uc_00f8ce3cfdfa3a311f298bb1a"
                             tableName="app_permission"/>

        <createTable tableName="app_authority">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_authority"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="name, institution_id" constraintName="uc_c3cd530e54ea9d6ba5ff98419"
                             tableName="app_authority"/>
        <addForeignKeyConstraint baseColumnNames="institution_id" baseTableName="app_authority"
                                 constraintName="FK_APP_AUTHORITY_ON_INSTITUTION" referencedColumnNames="id"
                                 referencedTableName="institution"/>

        <createTable tableName="app_authority_permission">
            <column name="authority_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_authority_permission"/>
            </column>
            <column name="permission_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_authority_permission"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="authority_id" baseTableName="app_authority_permission"
                                 constraintName="fk_appautper_on_authority" referencedColumnNames="id"
                                 referencedTableName="app_authority"/>
        <addForeignKeyConstraint baseColumnNames="permission_id" baseTableName="app_authority_permission"
                                 constraintName="fk_appautper_on_permission" referencedColumnNames="id"
                                 referencedTableName="app_permission"/>

        <createTable tableName="business_nature">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_business_nature"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_a419ec8cfe843a3db6e19c11b"
                             tableName="business_nature"/>

        <createTable tableName="country">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_country"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_33bcd9f8c7085f7aea93fff5c" tableName="country"/>

        <createTable tableName="currency">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_currency"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_4c79aedf07f9818cd8c87fd21" tableName="currency"/>

        <createTable tableName="education_level">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_education_level"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_cb9818c4ec7fe2d215343e14e"
                             tableName="education_level"/>

        <createTable tableName="employment_type">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_employment_type"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_d984be11c68db4174a17c8689"
                             tableName="employment_type"/>

        <createTable tableName="financial_goal">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_financial_goal"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_73a0964336e8c847d9767cb78"
                             tableName="financial_goal"/>

        <createTable tableName="interest">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_interest"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_09b111a1d9d5137b68ec659b7" tableName="interest"/>

        <createTable tableName="nationality">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_nationality"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_a821f98bff48f544eeba91ba8" tableName="nationality"/>

        <createTable tableName="occupation_group">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_occupation_group"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
        </createTable>
        <addUniqueConstraint columnNames="name" constraintName="uc_8dc56796e11a841441fb4b1d7"
                             tableName="occupation_group"/>

        <createTable tableName="state">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_state"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)"/>
            <column name="country_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code" constraintName="uc_33f124a67b8e053de11aa995a" tableName="state"/>
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="state" constraintName="FK_STATE_ON_COUNTRY"
                                 referencedColumnNames="id" referencedTableName="country"/>

        <createTable tableName="app_user">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="ref_no" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="username" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="password_hash" type="VARCHAR(60)"/>
            <column name="provider" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(200)"/>
            <column name="email" type="VARCHAR(100)"/>
            <column defaultValueBoolean="false" name="email_verified" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="phone_country" type="VARCHAR(3)"/>
            <column name="phone_number" type="VARCHAR(20)"/>
            <column defaultValueBoolean="false" name="phone_verified" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="lang_key" type="VARCHAR(6)"/>
            <column defaultValueBoolean="false" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="login_fail_attempt" type="INT(2)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="ADMIN" name="login_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="nric" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="address1" type="VARCHAR(100)"/>
            <column name="address2" type="VARCHAR(100)"/>
            <column name="address3" type="VARCHAR(100)"/>
            <column name="postcode" type="VARCHAR(100)"/>
            <column name="country_id" type="VARCHAR(36)"/>
            <column name="state_id" type="VARCHAR(36)"/>
            <column name="nationality_id" type="VARCHAR(36)"/>
            <column name="dob" type="DATE"/>
            <column name="gender" type="VARCHAR(10)"/>
            <column name="marital_status" type="VARCHAR(15)"/>
            <column name="ethnicity" type="VARCHAR(20)"/>
            <column name="religion" type="VARCHAR(20)"/>
            <column name="currency_id" type="VARCHAR(36)"/>
            <column name="epf_contribution" type="DECIMAL(5,2)"/>
            <column name="gross_monthly_income" type="DECIMAL(12,2)"/>
            <column name="socso" type="BOOLEAN"/>
            <column name="eis" type="BOOLEAN"/>
            <column name="education_level_id" type="VARCHAR(36)"/>
            <column name="employment_type_id" type="VARCHAR(36)"/>
            <column name="occupation_group_id" type="VARCHAR(36)"/>
            <column name="business_nature_id" type="VARCHAR(36)"/>
            <column name="self_employed_name" type="VARCHAR(100)"/>
        </createTable>
        <addUniqueConstraint columnNames="username, login_type" constraintName="uc_c6ecd75091fd6ae190d6857fe"
                             tableName="app_user"/>
        <addForeignKeyConstraint baseColumnNames="business_nature_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_BUSINESS_NATURE" referencedColumnNames="id"
                                 referencedTableName="business_nature"/>
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_COUNTRY" referencedColumnNames="id"
                                 referencedTableName="country"/>
        <addForeignKeyConstraint baseColumnNames="currency_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_CURRENCY" referencedColumnNames="id"
                                 referencedTableName="currency"/>
        <addForeignKeyConstraint baseColumnNames="education_level_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_EDUCATION_LEVEL" referencedColumnNames="id"
                                 referencedTableName="education_level"/>
        <addForeignKeyConstraint baseColumnNames="employment_type_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_EMPLOYMENT_TYPE" referencedColumnNames="id"
                                 referencedTableName="employment_type"/>
        <addForeignKeyConstraint baseColumnNames="nationality_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_NATIONALITY" referencedColumnNames="id"
                                 referencedTableName="nationality"/>
        <addForeignKeyConstraint baseColumnNames="occupation_group_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_OCCUPATION_GROUP" referencedColumnNames="id"
                                 referencedTableName="occupation_group"/>
        <addForeignKeyConstraint baseColumnNames="state_id" baseTableName="app_user"
                                 constraintName="FK_APP_USER_ON_STATE" referencedColumnNames="id"
                                 referencedTableName="state"/>

        <createTable tableName="app_user_authority">
            <column name="authority_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_authority"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_authority"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="authority_id" baseTableName="app_user_authority"
                                 constraintName="fk_appuseaut_on_authority" referencedColumnNames="id"
                                 referencedTableName="app_authority"/>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="app_user_authority"
                                 constraintName="fk_appuseaut_on_user" referencedColumnNames="id"
                                 referencedTableName="app_user"/>

        <createTable tableName="app_user_institution">
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_institution"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_institution"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="institution_id" baseTableName="app_user_institution"
                                 constraintName="fk_appuseins_on_institution" referencedColumnNames="id"
                                 referencedTableName="institution"/>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="app_user_institution"
                                 constraintName="fk_appuseins_on_user" referencedColumnNames="id"
                                 referencedTableName="app_user"/>

        <createTable tableName="app_user_financial_goal">
            <column name="financial_goal_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_financial_goal"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_financial_goal"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="financial_goal_id" baseTableName="app_user_financial_goal"
                                 constraintName="fk_appusefingoa_on_financial_goal" referencedColumnNames="id"
                                 referencedTableName="financial_goal"/>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="app_user_financial_goal"
                                 constraintName="fk_appusefingoa_on_user" referencedColumnNames="id"
                                 referencedTableName="app_user"/>

        <createTable tableName="app_user_interest">
            <column name="interest_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_interest"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_interest"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="interest_id" baseTableName="app_user_interest"
                                 constraintName="fk_appuseint_on_interest" referencedColumnNames="id"
                                 referencedTableName="interest"/>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="app_user_interest"
                                 constraintName="fk_appuseint_on_user" referencedColumnNames="id"
                                 referencedTableName="app_user"/>

        <createTable tableName="app_user_key_request">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_app_user_key_request"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="username" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="key_value" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="initial_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="completion_date" type="DATETIME"/>
        </createTable>

        <createTable tableName="system_configuration">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_system_configuration"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="code" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="VARCHAR(500)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="code, institution_id" constraintName="uc_de5da2ca267369fbb7e0ebb82"
                             tableName="system_configuration"/>
    </changeSet>
</databaseChangeLog>
