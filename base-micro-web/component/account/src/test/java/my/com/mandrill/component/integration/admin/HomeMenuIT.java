package my.com.mandrill.component.integration.admin;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import my.com.mandrill.component.constant.HomeMenuStatusEnum;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuMap;
import my.com.mandrill.component.domain.HomeMenuProduct;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import my.com.mandrill.component.dto.response.HomeMenuInfoResponse;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.HomeMenuMapRepository;
import my.com.mandrill.component.repository.jpa.HomeMenuProductRepository;
import my.com.mandrill.component.repository.jpa.HomeMenuRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

class HomeMenuIT extends BaseIntegrationTest {

	private static final String HOME_MENU_PARENT_ID = "9343123a-a276-42eb-b28d-2314ebb529e1";

	private static final String HOME_MENU_PARENT_CHILD = "1c4422d0-54f4-4538-8071-0e54aa929efb";

	private static final String HOME_MENU_PRODUCT = "c3771970-fc52-4c18-9f35-32ddcdc925ec";

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private HomeMenuRepository homeMenuRepository;

	@Autowired
	private HomeMenuMapRepository homeMenuMapRepository;

	@Autowired
	private HomeMenuProductRepository homeMenuProductRepository;

	@AfterEach
	public void clearEach() {
		homeMenuProductRepository.findById(HOME_MENU_PRODUCT).ifPresent(homeMenuProductRepository::delete);
		homeMenuRepository.findById(HOME_MENU_PARENT_CHILD).ifPresent(homeMenuRepository::delete);
		homeMenuRepository.findById(HOME_MENU_PARENT_ID).ifPresent(homeMenuRepository::delete);
	}

	@Test
	void addProducts_success() throws Exception {
		String jsonRequest = getRequest();

		MvcResult mvcResult = mockMvc
				.perform(post("/admin/home-menus/products").content(jsonRequest)
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		HomeMenuDTO result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<HomeMenuDTO>() {
				});

		Assertions.assertNotNull(result);
		Assertions.assertNotNull(result.getId());
		Assertions.assertNotNull(result.getHomeMenuProduct());
		Assertions.assertEquals(1, result.getHomeMenuProduct().size());

		HomeMenu homeMenu = homeMenuRepository.findById(result.getId()).orElse(null);
		Assertions.assertNotNull(homeMenu);

		for (HomeMenuMap map : homeMenu.getHomeMenuMaps()) {
			map.setHomeMenu(null);
		}
		homeMenuMapRepository.deleteAll(homeMenu.getHomeMenuMaps());
		homeMenuRepository.delete(homeMenu);
	}

	@Test
	void updateProducts_success() throws Exception {
		String jsonRequest = getRequest();

		MvcResult mvcResult = mockMvc
				.perform(put("/admin/home-menus/products/9ec29427-84f6-43b5-a30e-43fa35a81b71").content(jsonRequest)
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		HomeMenuDTO result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<HomeMenuDTO>() {
				});

		Assertions.assertNotNull(result);
		Assertions.assertNotNull(result.getId());
		Assertions.assertNotNull(result.getHomeMenuProduct());
		Assertions.assertEquals(1, result.getHomeMenuProduct().size());

		HomeMenu homeMenu = homeMenuRepository.findById(result.getId()).orElse(null);
		Assertions.assertNotNull(homeMenu);
	}

	@Test
	void deleteProducts_success() throws Exception {

		MvcResult mvcResult = mockMvc.perform(delete("/admin/home-menus/products/" + HOME_MENU_PARENT_ID)
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		HomeMenuProduct homeMenuProduct = homeMenuProductRepository.findById(HOME_MENU_PRODUCT).orElse(null);
		Assertions.assertNull(homeMenuProduct);
	}

	@Test
	void createHomeMenu_success() throws Exception {
		String jsonRequest = getRequest();

		MvcResult mvcResult = mockMvc
				.perform(post("/admin/home-menus").content(jsonRequest)
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		HomeMenuDTO result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<HomeMenuDTO>() {
				});

		Assertions.assertNotNull(result);
		Assertions.assertNotNull(result.getId());

		HomeMenu homeMenu = homeMenuRepository.findById(result.getId()).orElse(null);
		Assertions.assertNotNull(homeMenu);

		for (HomeMenuMap map : homeMenu.getHomeMenuMaps()) {
			map.setHomeMenu(null);
		}
		homeMenuMapRepository.deleteAll(homeMenu.getHomeMenuMaps());
		homeMenuRepository.delete(homeMenu);
	}

	@Test
	void findById_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/" + HOME_MENU_PARENT_ID)
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		HomeMenuDTO result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});

		Assertions.assertNotNull(result);

		HomeMenuDTO expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertEquals(expected, result);
	}

	@Test
	void deleteHomeMenu_success() throws Exception {
		MvcResult mvcResult = mockMvc.perform(delete("/admin/home-menus/1c4422d0-54f4-4538-8071-0e54aa929efb")
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent()).andReturn();

		HomeMenu homeMenu = homeMenuRepository.findById("1c4422d0-54f4-4538-8071-0e54aa929efb").orElse(null);
		Assertions.assertNull(homeMenu);
	}

	@Test
	void updateHomeMenu_success() throws Exception {
		String jsonRequest = getRequest();

		MvcResult mvcResult = mockMvc
				.perform(put("/admin/home-menus/1c4422d0-54f4-4538-8071-0e54aa929efb").content(jsonRequest)
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		HomeMenuDTO result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<HomeMenuDTO>() {
				});

		Assertions.assertNotNull(result);
		Assertions.assertNotNull(result.getId());
		Assertions.assertEquals("Integration Test Home Menu Update", result.getName());
		Assertions.assertEquals("Description of Integration Test Home Menu Update", result.getDescription());
		Assertions.assertEquals("INTEGRATION_TEST_UPDATE", result.getCode());
		Assertions.assertEquals(result.getState(), HomeMenuStatusEnum.ENABLED);
		Assertions.assertNull(result.getSequence());
	}

	@Test
	void findStateEnabled_success() throws Exception {

		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/state/enabled").queryParam("name", "OYEN_INSURANCE")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<HomeMenuInfoResponse> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});

		Assertions.assertFalse(result.isEmpty());
		Assertions.assertEquals(1, result.size());
		Assertions.assertNotNull(result.get(0).getId());

		HomeMenu homeMenu = homeMenuRepository.findById(result.get(0).getId()).orElse(null);
		Assertions.assertNotNull(homeMenu);
		Assertions.assertEquals(HomeMenuStatusEnum.ENABLED, homeMenu.getState());
	}

	@Test
	void findWithoutParentId_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/without-parent-id")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		List<HomeMenuDTO> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<List<HomeMenuDTO>>() {
				});

		Assertions.assertNotNull(result);
		HomeMenuDTO homeMenuDTO = result.stream().findFirst().get();

		Assertions.assertNull(homeMenuDTO.getParent());
	}

	@Test
	void findAll_success() throws Exception {

		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus").queryParam("code", "OYEN_INSURANCE").queryParam("page", "0")
						.queryParam("size", "10").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Page<HomeMenuDTO> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Page<HomeMenuDTO>>() {
				});

		Assertions.assertNotNull(result);
	}

	@Test
	void findPaginationWithoutParentId_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/pagination/without-parent-id").queryParam("code", "OYEN_INSURANCE")
						.queryParam("page", "0").queryParam("size", "10")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Page<HomeMenuDTO> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});

		Assertions.assertNotNull(result);
	}

	@Test
	void findPaginationWithParentId_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/pagination/with-parent-id").queryParam("code", "HOME_MENU_PARENT_CHILD")
						.queryParam("page", "0").queryParam("size", "10")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Page<HomeMenuDTO> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<>() {
				});
		Assertions.assertNotNull(result);

		List<HomeMenuDTO> expected = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expected);
		Assertions.assertEquals(expected, result.getContent());
	}

	@Test
	void findAllProducts_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/admin/home-menus/products").queryParam("code", "ALLIANZ_INSURANCE")
						.queryParam("page", "0").queryParam("size", "10")
						.header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON)).andReturn();

		Page<HomeMenuDTO> result = jsonUtil.convertValueFromJson(mvcResult.getResponse().getContentAsString(),
				new TypeReference<Page<HomeMenuDTO>>() {
				});

		Assertions.assertNotNull(result);
	}

	@SneakyThrows
	public String getRequest() {
		Path requestPath = Paths.get("src/test/resources/" + getBasePath().getBaseScenario() + "/request.json");
		return Files.readString(requestPath);
	}

}
