package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.consumer.DailyWelcomeNotificationScheduler;
import my.com.mandrill.component.dto.request.SmsOtpRequest;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.feign.dto.SchedulerPushNotificationDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.SchedulerMessaging;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.*;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class DailyWelcomeSchedulerIT extends BaseIntegrationTest {

	private static final String USER_ID = "c02a3444-bb2d-4cb5-92ef-fee9616528fe";

	private static final String DAILY_WELCOME_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID = "49ee34db-d7a2-4091-9edd-c92a4d2afc41";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private DailyWelcomeNotificationScheduler dailyWelcomeNotificationScheduler;

	@MockBean
	private KafkaSender kafkaSender;

	@AfterEach
	void tearEach() {
		userRepository.findById(USER_ID).ifPresent(userRepository::delete);
	}

	@Test
	void createWelcomeNotification_success() {
		Mockito.doNothing().when(kafkaSender).safeSend(Mockito.eq(KafkaTopic.SCHEDULER_PUSH_NOTIFICATION),
				Mockito.any(), Mockito.argThat(payload -> {
					if (!(payload instanceof SchedulerPushNotificationDTO req)) {
						return false;
					}
					return req.getUserIds().contains(USER_ID) && req.getSchedulerPushNotificationTemplateId()
							.equals(DAILY_WELCOME_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID);
				}));
		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM)
				.destination(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM).timeJobTriggered(Instant.now()).build();
		kafkaTemplate.send(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM, jsonUtil.convertToString(message));
		Awaitility.await().atMost(20, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Mockito.verify(kafkaSender, Mockito.times(1)).safeSend(Mockito.eq(KafkaTopic.SCHEDULER_PUSH_NOTIFICATION),
					Mockito.any(), Mockito.argThat(payload -> {
						if (!(payload instanceof SchedulerPushNotificationDTO req)) {
							return false;
						}
						return req.getUserIds().contains(USER_ID) && req.getSchedulerPushNotificationTemplateId()
								.equals(DAILY_WELCOME_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID);
					}));
		});

	}

	@Test
	void createWelcomeNotification_noProcessedUser_success() {
		SchedulerMessaging message = SchedulerMessaging.builder().key(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM)
				.destination(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM).timeJobTriggered(Instant.now()).build();
		kafkaTemplate.send(KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM, jsonUtil.convertToString(message));
		Mockito.verifyNoInteractions(kafkaSender);
	}

	@Test
	public void dltHandle() {
		dailyWelcomeNotificationScheduler.dltHandler("error");
	}

}
