{"request": {"method": "GET", "urlPath": "/user-interested-redirects/integration/count-entity"}, "response": {"body": "[\n  { \"entityName\": \"VEHICLE\", \"count\": 10 },\n  { \"entityName\": \"PROPERTY\", \"count\": 23 },\n  { \"entityName\": \"PROPERTY_STAGING\", \"count\": 12 },\n  { \"entityName\": \"UTILITY\", \"count\": 11 },\n  { \"entityName\": \"BANK\", \"count\": 50 },\n  { \"entityName\": \"CREDIT_CARD\", \"count\": 30 },\n  { \"entityName\": \"INSURANCE\", \"count\": 10 },\n  { \"entityName\": \"LOAN\", \"count\": 20 },\n  { \"entityName\": \"BANKLIST\", \"count\": 50 },\n  { \"entityName\": \"USER\", \"count\": 60 },\n  { \"entityName\": \"LEGAL\", \"count\": 77 },\n  { \"entityName\": \"GET_TO_KNOW_CC\", \"count\": 2 },\n  { \"entityName\": \"ADVERTISEMENT\", \"count\": 19 },\n  { \"entityName\": \"KNOW_YOUR_LOAN_LIMIT\", \"count\": 20 },\n  { \"entityName\": \"LOAN_LIMIT\", \"count\": 40 },\n  { \"entityName\": \"BANNER\", \"count\": 40 },\n  { \"entityName\": \"FINOLOGY_VEHICLE_INSURANCE\", \"count\": 30 },\n  { \"entityName\": \"M_AND_A_INTEREST\", \"count\": 20 },\n  { \"entityName\": \"SOLAROO\", \"count\": 10 },\n  { \"entityName\": \"EWILL\", \"count\": 60 },\n  { \"entityName\": \"STASHAWAY\", \"count\": 70 },\n  { \"entityName\": \"AHAM_CAPITAL\", \"count\": 430 },\n  { \"entityName\": \"JOMHIBAH\", \"count\": 23 },\n  { \"entityName\": \"SINEGY_DAX\", \"count\": 53 },\n  { \"entityName\": \"CREDIT_BUREAU\", \"count\": 43 },\n  { \"entityName\": \"EXPENSE\", \"count\": 32 },\n  { \"entityName\": \"EASIWILL\", \"count\": 21 },\n  { \"entityName\": \"CTOS\", \"count\": 43 },\n  { \"entityName\": \"INCOME\", \"count\": 54 },\n  { \"entityName\": \"VAULT\", \"count\": 34 },\n  { \"entityName\": \"E_ACCESS\", \"count\": 43 },\n  { \"entityName\": \"ATX\", \"count\": 34 },\n  { \"entityName\": \"MILIEU_SOLAR\", \"count\": 56 },\n  { \"entityName\": \"PAYMENT\", \"count\": 76 },\n  { \"entityName\": \"MODULE\", \"count\": 56 },\n  { \"entityName\": \"REDIRECT\", \"count\": 45 },\n  { \"entityName\": \"CGS_INT_MY\", \"count\": 34 },\n  { \"entityName\": \"INVESTMENT\", \"count\": 76 },\n  { \"entityName\": \"SAVING_GOAL\", \"count\": 56 },\n  { \"entityName\": \"DWS\", \"count\": 43 }\n]\n", "headers": {"Content-Type": "application/json"}, "status": 200}}