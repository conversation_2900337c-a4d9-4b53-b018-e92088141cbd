{"request": {"method": "POST", "urlPath": "/attachment/upload", "bodyPatterns": [{"matchesJsonPath": "$.attachments[?(@.name == 'InstitutionITLogo.png')]"}, {"matchesJsonPath": "$.className", "contains": "Institution"}, {"matchesJsonPath": "$.type", "contains": "INSTITUTION_IMAGE"}]}, "response": {"body": "{\n  \"attachmentGroupId\": \"2f762a3f-4541-4b26-ad01-f1a91d04d6a5\",\n  \"attachments\": [{\n    \"id\": \"66c7a9b0-1c0a-4aa3-8414-2ea0c833cc00\",\n    \"name\": \"institution.png\",\n    \"type\": \"image\",\n    \"url\": \"www.integration.com\"\n  }],\n  \"path\": \"/testing/institution/e462bfc4-091e-4b70-85c7-2ad5fcf3ba06.png\"\n}", "headers": {"Content-Type": "application/json"}, "status": 200}}