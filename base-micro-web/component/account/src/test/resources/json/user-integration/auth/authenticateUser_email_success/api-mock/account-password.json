{"request": {"method": "POST", "urlPath": "/authenticate/password", "bodyPatterns": [{"equalToJson": "{\"accessType\": \"EMAIL\",\"loginType\": \"USER\",\"passcodeType\": \"PASSWORD\",\"username\": \"deniar@localhost\",\"password\": \"Mandrill@123\"}"}]}, "response": {"body": "{\n    \"permissions\": [\n        \"USER_READ\",\n        \"SYSTEM_CONFIGURATION_DELETE\",\n        \"RSM_HEADER_READ\",\n        \"UTILITY_TYPE_READ\",\n        \"NOTIFICATION_PER_USER_READ\",\n        \"MESSAGE_TEMPLATE_HEADER_UPDATE\",\n        \"<PERSON><PERSON><PERSON><PERSON>_USER_READ\",\n        \"USER_UPDATE\",\n        \"USER_PLAYER_PER_ID_READ\",\n        \"USER_PLAYER_CREATE\",\n        \"INSTITUTION_UPDATE\",\n        \"USER_CREATE\",\n        \"SYSTEM_CONFIGURATION_CREATE\",\n        \"USER_DELETE\",\n        \"MESSAGE_TEMPLATE_HEADER_CREATE\",\n        \"SYSTEM_CONFIGURATION_READ\",\n        \"INSTITUTION_READ\",\n        \"RSM_LEAD_READ\",\n        \"USER_PLAYER_READ\",\n        \"MESSAGE_TEMPLATE_HEADER_READ\",\n        \"MO<PERSON><PERSON>_USER_UPDATE\",\n        \"INSTITUTION_DELETE\",\n        \"SYSTEM_CONFIGURATION_UPDATE\",\n        \"PERMISSION_READ\",\n        \"<PERSON>ER_PLAYER_UPDATE\"\n    ],\n    \"refNo\": \"8888888\"\n}", "headers": {"Content-Type": "application/json"}, "status": 200}}