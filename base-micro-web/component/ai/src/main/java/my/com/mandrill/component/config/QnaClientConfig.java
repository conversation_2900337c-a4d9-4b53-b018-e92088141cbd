package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.QnaClient;
import my.com.mandrill.utilities.general.util.ConnectionUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.netty.http.client.HttpClient;

@Configuration
@RequiredArgsConstructor
public class QnaClientConfig {

	private final IntegrationAIProperties integrationAIProperties;

	@Bean
	public WebClient qnaWebClient() {
		return WebClient.builder().baseUrl(integrationAIProperties.getQnaUri()).clientConnector(
				new ReactorClientHttpConnector(HttpClient.create(ConnectionUtil.getDefaultConnectionProvider())))
				.build();
	}

	@Bean
	public QnaClient qnaClient(WebClient qnaWebClient) {
		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
				.builder(WebClientAdapter.forClient(qnaWebClient))
				.blockTimeout(integrationAIProperties.getQnaBlockingTimeout()).build();
		return httpServiceProxyFactory.createClient(QnaClient.class);
	}

}
