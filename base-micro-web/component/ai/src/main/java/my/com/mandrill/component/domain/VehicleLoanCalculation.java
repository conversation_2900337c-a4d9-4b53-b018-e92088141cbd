package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.component.constant.VehicleLoanCalculationEventType;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "vehicle_loan_calculation")
public class VehicleLoanCalculation extends AuditSection {

	@NotBlank
	@Size(max = 36)
	@Column(name = "user_id", nullable = false, length = 36)
	private String userId;

	@NotBlank
	@Column(name = "vehicle_id", length = 50)
	private String vehicleId;

	@NotBlank
	@Column(name = "vehicle_brand", length = 50)
	private String vehicleBrand;

	@NotBlank
	@Column(name = "vehicle_model", length = 50)
	private String vehicleModel;

	@NotBlank
	@Column(name = "vehicle_variant", length = 100)
	private String vehicleVariant;

	@Column(name = "vehicle_price", nullable = false)
	private BigDecimal vehiclePrice;

	@Column(name = "monthly_installment", nullable = false)
	private BigDecimal monthlyInstallment;

	@Column(name = "down_payment", nullable = false)
	private BigDecimal downPayment;

	@Column(name = "down_payment_percentage", nullable = false)
	private BigDecimal downPaymentPercentage;

	@Column(name = "loan_amount", nullable = false)
	private BigDecimal loanAmount;

	@Column(name = "interest_rate", nullable = false)
	private BigDecimal interestRate;

	@Column(name = "loan_period", nullable = false)
	private Short loanPeriod;

	@Enumerated(EnumType.STRING)
	@Column(name = "event_type", nullable = false, length = 50)
	private VehicleLoanCalculationEventType eventType;

}
