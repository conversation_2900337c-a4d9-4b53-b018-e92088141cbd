package my.com.mandrill.component.dto.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.component.dto.response.UserDataAIResponse;

import java.util.ArrayList;
import java.util.List;

@Data
@Getter
@Setter
public class ForYouDTO {

	private String module;

	private List<String> interests = new ArrayList<>();

	private UserDataAIResponse user_data;

	private Object filters;

}
