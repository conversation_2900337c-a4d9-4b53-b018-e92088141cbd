package my.com.mandrill.component.dto.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class KyllDataDTO {

	private BigDecimal dsr;

	private BigDecimal maximumMonthlyLoanEligibility;

	private BigDecimal totalMonthlyCommitments;

	private BigDecimal balanceMonthlyLoanEligibility;

	private BigDecimal maxHomeLoanAmount;

	private BigDecimal maxCarLoanAmount;

	private BigDecimal maxPersonalLoanAmount;

	private BigDecimal idealHomeLoanAmount;

	private BigDecimal idealHomeLoanMonthlyInstallment;

	private BigDecimal idealCarLoanAmount;

	private BigDecimal idealCarLoanMonthlyInstallment;

	private BigDecimal idealPersonalLoanAmount;

	private BigDecimal idealPersonalMonthlyInstallment;

}
