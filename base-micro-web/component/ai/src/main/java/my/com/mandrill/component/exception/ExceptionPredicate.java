package my.com.mandrill.component.exception;

import jakarta.persistence.EntityNotFoundException;

import java.util.function.Supplier;

public final class ExceptionPredicate {

	public static final String NOT_SUPPORTED_MESSAGE_FORMAT = "%s not supported";

	private ExceptionPredicate() {
		throw new IllegalStateException("Utility class");
	}

	public static Supplier<NotSupportedException> notSupportedByJourneyConfigurationGroupType(String groupType) {
		return () -> new NotSupportedException(String.format(NOT_SUPPORTED_MESSAGE_FORMAT, groupType));
	}

	public static Supplier<EntityNotFoundException> productGroupNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Product group with id=%s was not found", id));
	}

	public static Supplier<EntityNotFoundException> productTypeNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Product type with id=%s was not found", id));
	}

}
