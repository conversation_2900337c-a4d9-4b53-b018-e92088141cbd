package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.VehicleApplication;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface VehicleApplicationRepository extends JpaRepository<VehicleApplication, String> {

	boolean existsByUserIdAndVehicleId(String userId, String vehicleId);

	List<VehicleApplication> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanEqualOrderByCreatedDateAsc(
			@NonNull Instant startDate, @NonNull Instant endDate);

	Page<VehicleApplication> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanEqual(@NonNull Instant startDate,
			@NonNull Instant endDate, Pageable pageable);

}
