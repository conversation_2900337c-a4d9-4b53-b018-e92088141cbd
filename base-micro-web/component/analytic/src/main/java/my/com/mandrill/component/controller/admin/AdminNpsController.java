package my.com.mandrill.component.controller.admin;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.model.NpsChartSeriesDTO;
import my.com.mandrill.component.dto.response.NPSSurveyResponse;
import my.com.mandrill.component.dto.response.NpsDetailedFeedbackResponse;
import my.com.mandrill.component.service.NpsAggregateService;
import my.com.mandrill.component.service.SurveyIntegrationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.Year;
import java.util.Objects;

@RestController
@RequestMapping("/admin/nps")
@RequiredArgsConstructor
public class AdminNpsController {

	private final NpsAggregateService npsAggregateService;

	private final SurveyIntegrationService surveyIntegrationService;

	@GetMapping("summary-series")
	public NpsChartSeriesDTO summarySeries(@RequestParam Year year) {
		return npsAggregateService.getChartSeries(year);
	}

	@GetMapping("/nps-survey")
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_READ)")
	public ResponseEntity<Page<NPSSurveyResponse>> getNPSSurveyList(
			@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable,
			@RequestParam(required = false) LocalDate startDate, @RequestParam(required = false) LocalDate endDate) {
		return ResponseEntity.ok(surveyIntegrationService.findAllNPSSurveyResponses(pageable, startDate, endDate));
	}

	@GetMapping("detailed-feedback")
	public NpsDetailedFeedbackResponse detailedFeedback(@RequestParam(required = false) LocalDate startDate,
			@RequestParam(required = false) LocalDate endDate) {

		if (Objects.isNull(startDate) && Objects.isNull(endDate)) {
			startDate = LocalDate.now().withDayOfMonth(1);
			endDate = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
		}

		return npsAggregateService.getDetailedFeedback(startDate, endDate);

	}

}
