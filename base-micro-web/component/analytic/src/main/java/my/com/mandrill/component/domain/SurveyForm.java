package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.SurveyFormStatus;
import my.com.mandrill.utilities.general.constant.SurveyFormType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "survey_form", uniqueConstraints = @UniqueConstraint(columnNames = { "name" }))
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SurveyForm extends AuditSection implements Serializable {

	@NotBlank
	@Size(max = 36)
	@Column(name = "id", nullable = false, length = 36)
	private String id;

	@NotBlank
	@Size(max = 36)
	@Column(name = "name", nullable = false, length = 36)
	private String name;

	@Size(max = 36)
	@Column(name = "description", nullable = false, length = 36)
	private String description;

	@Column(name = "active")
	private boolean active;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "status", length = 36)
	private SurveyFormStatus status;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "form_type")
	private SurveyFormType formType = SurveyFormType.WELCOME_APP;

	@NotEmpty
	@ToString.Exclude
	@OrderBy("seq ASC")
	@OneToMany(fetch = FetchType.EAGER, mappedBy = "surveyForm", cascade = CascadeType.ALL, orphanRemoval = true)
	private List<SurveyQuestion> surveyQuestions = new ArrayList<>();

	@NotNull
	@Column(name = "deleted", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private boolean deleted = false;

}
