package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.component.constant.ProductConfigurationTemplateField;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.ProductConfigTemplateValidationType;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "product_configuration_template")
public class ProductConfigurationTemplate extends AuditSection implements Serializable {

	@NotNull
	@Size(max = 255)
	@Column(name = "template_name")
	private String templateName;

	@Size(max = 36)
	@Column(name = "vault_type_id", length = 36)
	private String vaultTypeId;

	@Column(name = "document_type_id")
	private String documentTypeId;

	@Column(name = "file_name_prefix")
	private String fileNamePrefix;

	@Column(name = "label_en")
	private String labelEn;

	@Column(name = "label_my")
	private String labelMs;

	@Column(name = "label_cn")
	private String labelCn;

	@Column(name = "hint_en")
	private String hintEn;

	@Column(name = "hint_my")
	private String hintMs;

	@Column(name = "hint_cn")
	private String hintCn;

	@Size(max = 255)
	@Column(name = "description")
	private String description;

	@Column(name = "is_multiple", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	@Builder.Default
	private Boolean isMultiple = false;

	@Column(name = "is_required", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	@Builder.Default
	private Boolean isRequired = false;

	@Column(name = "active", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	@Builder.Default
	private Boolean active = false;

	@Column(name = "identifier_code")
	private String identifierCode;

	@Column(name = "validation_type")
	@Enumerated(EnumType.STRING)
	private ProductConfigTemplateValidationType validationType;

	@Column(name = "type_value")
	private Integer typeValue;

	@Column(name = "field_type")
	@Enumerated(EnumType.STRING)
	private ProductConfigurationTemplateField fieldType;

	@ToString.Exclude
	@Builder.Default
	@JsonManagedReference
	@OneToMany(fetch = FetchType.EAGER, mappedBy = "template")
	private Set<ProductConfiguration> products = new LinkedHashSet<>();

}
