package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.UserInterestStatus;

import java.time.Instant;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UserInterestRecordViewDTO {

	private String id;

	private String userRefNo;

	private String refNo;

	private String fullName;

	private String email;

	private String phoneNumber;

	private String productType;

	private String productName;

	private String issuerCode;

	private String issuerType;

	private Instant createdDate;

	private UserInterestStatus status;

	private String nric;

	private Boolean isBalanceTransfer = false;

	private Long vaultCount;

	private List<UserInterestRecordAddonDTO> addons;

}
