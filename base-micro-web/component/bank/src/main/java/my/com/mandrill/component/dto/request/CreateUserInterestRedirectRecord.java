package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.dto.model.HandleSource;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CreateUserInterestRedirectRecord implements HandleSource {

	@NotBlank
	@Size(max = 255)
	private String productType;

	@NotBlank
	@Size(max = 255)
	private String productName;

	@NotBlank
	@Size(max = 255)
	private String issuerCode;

	@NotBlank
	@Size(max = 255)
	private String issuerType;

	@NotBlank
	@Size(max = 36)
	private String productId;

	private Boolean isBalanceTransfer = false;

	private UserInterestedSource source;

	private String sourceId;

}
