<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <changeSet id="bank-component_20230912_wuikeat_0001" author="wuikeat">

        <createTable tableName="loan_limit">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_loan_limit"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_journey_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="home_loan_limit" type="DECIMAL(5, 2)"/>
            <column name="auto_loan_limit" type="DECIMAL(5, 2)"/>
            <column name="personal_loan_limit" type="DECIMAL(5, 2)"/>
        </createTable>
        <addUniqueConstraint columnNames="user_id" constraintName="uc_28f0f8af49d7b2f8938f72363" tableName="loan_limit"/>
    </changeSet>

</databaseChangeLog>