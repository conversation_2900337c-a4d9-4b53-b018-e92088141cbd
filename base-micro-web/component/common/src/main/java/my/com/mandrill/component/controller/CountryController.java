package my.com.mandrill.component.controller;

import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Country;
import my.com.mandrill.component.dto.model.CountryDTO;
import my.com.mandrill.component.service.CountryService;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("country")
public class CountryController {

	private final CountryService countryService;

	public CountryController(CountryService countryService) {
		this.countryService = countryService;
	}

	@GetMapping
	public ResponseEntity<List<CountryDTO>> getAllCountries(Sort sort) {
		List<Country> countries = countryService.findByActiveTrue(sort);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toCountryDTOList(countries));
	}

	@GetMapping("{id}")
	public ResponseEntity<CountryDTO> getCountry(@PathVariable String id) {
		Country country = countryService.findByIdAndActiveTrue(id);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toCountryDTO(country));
	}

	@GetMapping("/nationality-id")
	public ResponseEntity<CountryDTO> getCountryByNationalityId(@RequestParam String nationalityId) {
		Country country = countryService.findByNationalityIdAndActiveTrue(nationalityId);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toCountryDTO(country));
	}

}
