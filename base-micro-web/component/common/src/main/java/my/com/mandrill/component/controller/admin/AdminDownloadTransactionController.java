package my.com.mandrill.component.controller.admin;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.model.DownloadTransactionDTO;
import my.com.mandrill.component.service.DownloadTransactionIntegrationService;
import my.com.mandrill.component.service.DownloadTransactionService;
import my.com.mandrill.component.util.AttachmentUtil;
import my.com.mandrill.utilities.general.constant.DownloadTransactionType;
import my.com.mandrill.utilities.storage.model.OutputContentFile;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("admin/download-transaction")
public class AdminDownloadTransactionController {

	private final DownloadTransactionService downloadTransactionService;

	private final DownloadTransactionIntegrationService downloadTransactionIntegrationService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.DOWNLOAD_TRANSACTION_READ)")
	public Page<DownloadTransactionDTO> getTransactions(@RequestParam DownloadTransactionType type,
			@PageableDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
		return downloadTransactionService.findByType(type, pageable);
	}

	@GetMapping("{id}/download")
	@PreAuthorize("hasAuthority(@authorityPermission.DOWNLOAD_TRANSACTION_READ)")
	public ResponseEntity<Resource> download(@PathVariable String id) {
		OutputContentFile file = downloadTransactionIntegrationService.download(id);
		return AttachmentUtil.outputAttachment(file);
	}

}
