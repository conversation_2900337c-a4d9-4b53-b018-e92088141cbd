package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import my.com.mandrill.component.constants.SavingActivityType;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@FieldNameConstants
@Table(name = "saving_activity")
@EqualsAndHashCode(callSuper = true)
public class SavingActivity extends AuditSection implements Serializable {

	@NotNull
	@Column(name = "amount", length = 100, nullable = false)
	private BigDecimal amount;

	@Enumerated(EnumType.STRING)
	@Column(name = "type")
	private SavingActivityType type;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "saving_goal_id", nullable = false)
	private SavingGoal savingGoal;

	@Column(name = "transaction_date")
	private LocalDate transactionDate;

}
