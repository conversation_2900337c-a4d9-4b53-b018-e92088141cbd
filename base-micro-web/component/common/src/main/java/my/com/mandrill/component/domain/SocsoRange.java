package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "socso_range")
public class SocsoRange extends AuditSection implements Serializable {

	@NotNull
	@Digits(integer = 15, fraction = 2)
	@Column(name = "salary_from", nullable = false, precision = 15, scale = 2)
	private BigDecimal salaryFrom;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	@Column(name = "salary_to", nullable = false, precision = 15, scale = 2)
	private BigDecimal salaryTo;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	@Column(name = "share", nullable = false, precision = 15, scale = 2)
	private BigDecimal share;

}