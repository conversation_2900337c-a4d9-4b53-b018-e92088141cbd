package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "utility_type", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class UtilityType extends AuditSection {

	@Size(max = 255)
	@Column(name = "code", nullable = false)
	private String code;

	@Size(max = 255)
	@NotBlank
	@Column(name = "name", nullable = false)
	private String name;

	@Size(max = 255)
	@Column(name = "description")
	private String description;

	@NotNull
	@Column(name = "active", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
	private Boolean active = false;

	@Column(name = "url")
	private String url;

	@Size(max = 36)
	@Column(name = "attachment_group_id", length = 36)
	private String attachmentGroupId;

	@Column(name = "ocr_type", length = 50)
	private String ocrType;

	@PrePersist
	@PreUpdate
	private void save() {
		this.code = Objects.isNull(this.code) ? null : this.code.toUpperCase();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		UtilityType utilityType = (UtilityType) o;
		return getId() != null && Objects.equals(getId(), utilityType.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}