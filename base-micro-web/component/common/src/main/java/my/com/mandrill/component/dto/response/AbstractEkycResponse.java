package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Data
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AbstractEkycResponse implements Serializable {

	@JsonProperty("errorCode")
	private String errorCode;

	@JsonProperty("errorMessage")
	private String errorMessage;

	@JsonProperty("resultTimeStamp")
	private String resultTimeStamp;

}
