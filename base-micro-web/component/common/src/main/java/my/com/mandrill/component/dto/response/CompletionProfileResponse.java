package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.component.dto.model.CompletionCountDTO;

import java.util.List;

@Data
@Getter
@Setter
@AllArgsConstructor
public class CompletionProfileResponse {

	private List<CompletionCountDTO> modules;

	private int total;

	private int completed;

	private double percentage;

}
