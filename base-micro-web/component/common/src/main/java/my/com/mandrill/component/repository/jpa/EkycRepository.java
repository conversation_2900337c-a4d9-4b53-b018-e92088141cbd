package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Ekyc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EkycRepository extends JpaRepository<Ekyc, String> {

	Ekyc findByUserId(String userId);

	Optional<Ekyc> findByUserIdAndAttachmentGroupIdNotNull(String userId);

	Optional<Ekyc> findByApplicantId(String applicantId);

	@Modifying
	@Query("UPDATE Ekyc e SET e.attempt = 0 WHERE e.id = :id")
	void resetAttemptToZero(@Param("id") String id);

	// loongyeat:
	// [From requirements]
	// For item EKYC if the users completes either one sub item will count as Done. Users
	// do not need to complete all
	// three (3) sub items to count as Done.
	@Query("""
			select count(e) from Ekyc e
			where e.userId = :userId
			and (
			e.icStatus = 'SUCCESS'
			or e.photoStatus = 'SUCCESS'
			or e.overallStatus = 'SUCCESS'
			)
			""")
	Long countCompleted(String userId);

}