package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constants.BannerAttachmentDescriptionTypeEnum;
import my.com.mandrill.component.domain.Attachment;
import my.com.mandrill.component.domain.AttachmentGroup;
import my.com.mandrill.component.domain.VaultType;
import my.com.mandrill.component.dto.model.AttachmentGroupDTO;
import my.com.mandrill.component.dto.model.RewriteVaultDTO;
import my.com.mandrill.component.dto.request.AttachmentRequest;
import my.com.mandrill.component.dto.response.AttachmentGroupResponse;
import my.com.mandrill.component.dto.response.AttachmentResponse;
import my.com.mandrill.component.exception.CommonServiceException;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.AttachmentGroupRepository;
import my.com.mandrill.component.repository.jpa.AttachmentRepository;
import my.com.mandrill.component.service.AttachmentService;
import my.com.mandrill.component.service.BusinessLogicService;
import my.com.mandrill.component.service.VaultService;
import my.com.mandrill.crypto.exception.CryptoException;
import my.com.mandrill.crypto.service.CryptoService;
import my.com.mandrill.domain.ByteArraySecret;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.dto.request.BankListAttachmentRequestGroupDTO;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementAttachmentPopUpResponseDTO;
import my.com.mandrill.utilities.feign.dto.response.BannerAttachmentRequestDTO;
import my.com.mandrill.utilities.feign.dto.response.BannerAttachmentRequestGroupDTO;
import my.com.mandrill.utilities.feign.dto.response.BannerAttachmentResponseDTO;
import my.com.mandrill.utilities.general.constant.AttachmentTypeEnum;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import my.com.mandrill.utilities.general.constant.BankImageType;
import my.com.mandrill.utilities.general.constant.FileMethod;
import my.com.mandrill.utilities.general.dto.BankListDTO;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.DateConvertUtil;
import my.com.mandrill.utilities.general.util.ObjectMapperUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import my.com.mandrill.utilities.storage.model.InputContentFile;
import my.com.mandrill.utilities.storage.model.OutputContentFile;
import my.com.mandrill.utilities.storage.service.FileManager;
import my.com.mandrill.utilities.storage.util.LocalFileUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.AccessDeniedException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
@Transactional(readOnly = true)
@Slf4j
@RequiredArgsConstructor
public class AttachmentServiceImpl implements AttachmentService {

	private final AttachmentGroupRepository attachmentGroupRepository;

	private final AttachmentRepository attachmentRepository;

	private final BaseProperties baseProperties;

	private final BusinessLogicService businessLogicService;

	private final ObjectMapper objectMapper;

	private final VaultService vaultService;

	private final CryptoService<ByteArraySecret, ByteArraySecret> byteArraySecretCrypto;

	private FileManager fileManager;

	@Transactional
	@Override
	public <T> AttachmentGroup createAttachmentGroup(String entityName, String attachmentGroupParentId,
			String description, String name, String basePath, String groupAttachmentType, VaultType vaultType,
			Boolean isReminder, LocalDate docExpiryDate, String documentTypeId, String documentTypeName,
			Boolean isEditable) {
		return this.createAttachmentGroup(entityName, attachmentGroupParentId, description, name, basePath,
				groupAttachmentType, vaultType, isReminder, docExpiryDate, documentTypeId, documentTypeName, isEditable,
				null, null);
	}

	@Transactional
	@Override
	public <T> AttachmentGroup createAttachmentGroup(String entityName, String attachmentGroupParentId,
			String description, String name, String basePath, String groupAttachmentType, VaultType vaultType,
			Boolean isReminder, LocalDate docExpiryDate, String documentTypeId, String documentTypeName,
			Boolean isEditable, String identifierCode, String publicKeyId) {

		AttachmentGroup attachmentGroup = new AttachmentGroup();
		attachmentGroup.setEntityName(entityName.toUpperCase());
		attachmentGroup.setBasePath(basePath);
		attachmentGroup.setGroupAttachmentType(groupAttachmentType);
		if (attachmentGroupParentId != null) {
			attachmentGroup
					.setAttachmentGroupParent(attachmentGroupRepository.findById(attachmentGroupParentId).orElse(null));
		}
		attachmentGroup.setDescription(description);
		attachmentGroup.setName(name);
		attachmentGroup.setIsReminder(isReminder);
		if (publicKeyId == null || publicKeyId.isEmpty()) {
			attachmentGroup.setUserId(SecurityUtil.currentUserId());
		}
		attachmentGroup.setPublicKeyId(publicKeyId);
		attachmentGroup.setVaultType(vaultType);
		attachmentGroup.setDocExpiryDate(docExpiryDate);
		attachmentGroup.setDocumentTypeId(documentTypeId);
		attachmentGroup.setIsEditable(isEditable);
		attachmentGroup.setIdentifierCode(identifierCode);
		AttachmentGroup attachmentGroupResult = attachmentGroupRepository.save(attachmentGroup);
		String newPath = attachmentGroupResult.getId();
		if (basePath != null) {
			newPath = buildPath(basePath);
		}
		else {
			newPath = buildPathWithYear(newPath);
		}
		attachmentGroupResult.setBasePath(newPath);
		return attachmentGroupRepository.save(attachmentGroupResult);
	}

	@Transactional
	@Override
	public List<Attachment> saveAttachment(AttachmentGroup attachmentGroup, Set<Attachment> attachments,
			String reference) {
		List<Attachment> attachmentResult = new ArrayList<>();
		for (Attachment attachment : attachments) {
			if (StringUtils.isNotBlank(attachment.getId()) && Boolean.TRUE.equals(attachment.getRemoveFlag())) {
				deleteAttachmentById(attachment.getId(), attachmentGroup);
			}
			else {
				if (attachment.getBlobFile() != null) {
					attachment.setAttachmentGroup(attachmentGroup);
					attachmentRepository.save(attachment);
					if (attachment.getType().equals(AttachmentTypeEnum.Image.name())) {
						writeImageFiles(attachment);
					}
					else if (attachment.getType().equals(AttachmentTypeEnum.Video.name())) {
						writeNonImageFiles(attachment);
					}
					else {
						writeNonImageFiles(attachment);
					}
					attachmentGroupRepository.save(attachment.getAttachmentGroup());
					attachmentResult.add(attachment);

				}
				else if (StringUtils.isNotBlank(attachment.getId())) {
					Optional<Attachment> attachment1 = attachmentRepository.findById(attachment.getId());

					if (attachment1.isPresent()) {
						attachment.setAttachmentGroup(attachmentGroup);
						attachment = attachmentRepository.save(attachment);
						attachmentResult.add(attachment);
					}
				}
			}
		}
		return attachmentResult;
	}

	/**
	 * Convenience method for
	 * {@code saveAttachment(AttachmentGroup attachmentGroup, HashSet<Attachment> attachment, String reference)}
	 * where only one attachment is being saved.
	 */
	@Transactional
	private Attachment saveAndDeletePreviousAttachment(AttachmentGroup attachmentGroup, Attachment attachment,
			@NonNull BannerAttachmentDescriptionTypeEnum description) {
		if (attachmentGroup.getId() != null) {
			Optional<Attachment> previousAttachment = attachmentRepository
					.findByAttachmentGroupIdAndDescription(attachmentGroup.getId(), description.getDescription());
			previousAttachment.ifPresent(value -> deleteAttachmentById(value.getId(), attachmentGroup));
		}

		HashSet<Attachment> attachments = new HashSet<>();
		attachments.add(attachment);
		attachmentGroup.getAttachments().clear();
		return this.saveAttachment(attachmentGroup, attachments, null).get(0);
	}

	@Override
	public OutputContentFile downloadAttachment(String id) {
		Attachment attachment = attachmentRepository.findById(id)
				.orElseThrow(ExceptionPredicate.attachmentNotFound(id));
		this.fileManager = businessLogicService
				.getFileManager(attachment.getAttachmentGroup().getGroupAttachmentType());
		return this.fileManager.getFile(attachment.getAttachmentGroup().getBasePath(), attachment.getId(),
				attachment.getName());
	}

	@Override
	public List<Attachment> findAllAttachmentByGroupId(String attachmentGroupId) {
		return attachmentRepository.findAllByAttachmentGroupIdOrderByCreatedDateDesc(attachmentGroupId);
	}

	// TODO : only applicable for local
	// need to move to the fileManager to make it general base on local storage method
	@Override
	public String generateUrl(Attachment attachment) {
		this.fileManager = businessLogicService
				.getFileManager(attachment.getAttachmentGroup().getGroupAttachmentType());

		String method = businessLogicService
				.getAttachmentMethod(attachment.getAttachmentGroup().getGroupAttachmentType());
		if (method == null || method.equals("private")) {
			return "";
		}
		return this.fileManager.generateURL(baseProperties.getCdn().getHost(),
				attachment.getAttachmentGroup().getBasePath().replace("\\", "/"),
				LocalFileUtil.formatActualFile(attachment.getId(), attachment.getName()));
	}

	@Transactional
	@Override
	public List<Attachment> saveAttachment(AttachmentRequest attachmentRequest) {
		AttachmentGroup attachmentGroupResult = null;
		if (attachmentRequest.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(attachmentRequest.getClassName(),
					attachmentRequest.getAttachmentGroupParentId(), attachmentRequest.getDescription(),
					attachmentRequest.getName(), attachmentRequest.getPath(), attachmentRequest.getType(),
					attachmentRequest.getVaultType(), attachmentRequest.getIsReminder(),
					attachmentRequest.getDocExpiryDate(), attachmentRequest.getDocumentTypeId(),
					attachmentRequest.getDocumentTypeName(), attachmentRequest.getIsEditable(),
					attachmentRequest.getIdentifierCode(), attachmentRequest.getPublicKeyId());
		}
		else {
			attachmentGroupResult = attachmentGroupRepository.findById(attachmentRequest.getAttachmentGroupId())
					.orElseThrow(ExceptionPredicate.attachmentGroupNotFound(attachmentRequest.getAttachmentGroupId()));
			attachmentGroupResult.setName(attachmentRequest.getName());
			attachmentGroupResult.setDocExpiryDate(attachmentRequest.getDocExpiryDate());
			attachmentGroupResult.setIsReminder(attachmentRequest.getIsReminder());
			attachmentGroupResult.setIsEditable(attachmentRequest.getIsEditable());
			attachmentGroupRepository.save(attachmentGroupResult);
		}

		boolean isEncrypt = AttachmentTypeFileStorageEnum.valueOf(attachmentRequest.getType()).getMethod()
				.equals(FileMethod.PRIVATE.getMethod());

		List<Attachment> inputAttachments = attachmentRequest.getAttachments().stream().map(attachment -> {
			Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setSecretKey(attachmentRequest.getSecretKey());
			attachmentEntity.setTemp(attachmentRequest.getIsTemp());
			return attachmentEntity;
		}).toList();
		return this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null);
	}

	// TODO test
	private void removeFile(Attachment attachment) {
		this.fileManager.deleteFile(attachment.getAttachmentGroup().getBasePath(),
				LocalFileUtil.formatActualFile(attachment.getId(), attachment.getName()));
	}

	private Attachment writeNonImageFiles(Attachment attachment) {
		InputContentFile input = constructFromAttachment(attachment);
		this.fileManager.addFile(input);
		attachment.getAttachmentGroup().setBasePath(input.getPath());
		return attachment;
	}

	private Attachment writeImageFiles(Attachment attachment) {
		InputContentFile input = constructFromAttachment(attachment);
		this.fileManager.addImage(input);
		attachment.getAttachmentGroup().setBasePath(input.getPath());
		return attachment;
	}

	private InputContentFile constructFromAttachment(Attachment attachment) {
		InputContentFile inputContentFile = new InputContentFile();
		inputContentFile.setFileName(attachment.getName());
		inputContentFile.setPath(attachment.getAttachmentGroup().getBasePath());
		byte[] byteArray = Base64.getDecoder().decode(attachment.getBlobFile());

		if (attachment.isEncrypt()) {
			if (StringUtils.isBlank(attachment.getSecretKey())) {
				throw new CommonServiceException("Missing secretKey for attachment's encryption");
			}
			ByteArraySecret input = new ByteArraySecret(byteArray, attachment.getSecretKey());

			ByteArraySecret output;

			try {
				output = byteArraySecretCrypto.encrypt(input);
			}
			catch (CryptoException e) {
				log.error("Encryption error: {}", e.getMessage());
				throw new CommonServiceException("Failed to encrypt");
			}

			inputContentFile.setFile(output.getData());
		}
		else {
			inputContentFile.setFile(byteArray);
		}

		inputContentFile.setKey(attachment.getId());
		return inputContentFile;
	}

	private String buildPathWithYear(String path) {
		String year = DateConvertUtil.toString(Instant.now(), DateConvertUtil.DATE_FORMAT_4);
		return year + this.fileManager.pathSeparator() + path + this.fileManager.pathSeparator();
	}

	private String buildPath(String path) {
		return path + this.fileManager.pathSeparator();
	}

	private AttachmentGroupResponse handleAttachment(AttachmentRequest attachmentRequest, boolean forceEdit) {
		if (!forceEdit && (attachmentRequest.getAttachmentGroupId() != null)) {
			throwIfAttachmentGroupNotFoundOrNotEditable(attachmentRequest.getAttachmentGroupId());
		}

		this.fileManager = businessLogicService.getFileManager(attachmentRequest.getType());

		String attachmentBasePathBuilder = businessLogicService.defineAttachmentPathPrefix(attachmentRequest.getType(),
				attachmentRequest.getPublicKeyId());
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (attachmentRequest.getPath() != null && !attachmentRequest.getPath().isEmpty()) {
				attachmentRequest.setPath(buildPath(attachmentBasePathBuilder) + attachmentRequest.getPath());
			}
			else {
				attachmentRequest.setPath(attachmentBasePathBuilder);
			}
		}

		String attachmentGroupId = attachmentRequest.getAttachmentGroupId();
		List<Attachment> attachmentResult = saveAttachment(attachmentRequest);
		List<AttachmentResponse> attachmentDTOList = attachmentResult.stream()
				.map(attachment -> ObjectMapperUtil.MAPPER.convertValue(attachment, AttachmentResponse.class)).toList();
		if (attachmentGroupId == null && !attachmentResult.isEmpty()) {
			attachmentGroupId = attachmentResult.get(0).getAttachmentGroup().getId();
		}
		AttachmentGroupResponse attachmentGroupResponse = new AttachmentGroupResponse();
		attachmentGroupResponse.setAttachmentGroupId(attachmentGroupId);
		attachmentGroupResponse.setAttachments(attachmentDTOList);

		String method = businessLogicService.getAttachmentMethod(attachmentRequest.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(attachmentRequest.getPath());
		attachmentGroupResponse.setPath(responsePath.toString());
		return attachmentGroupResponse;
	}

	@Transactional
	@Override
	public AttachmentGroupResponse handleAttachment(AttachmentRequest attachmentRequest) {
		return handleAttachment(attachmentRequest, false);
	}

	@Transactional
	@Override
	public AttachmentGroupResponse handleAttachmentForced(AttachmentRequest attachmentRequest) {
		return handleAttachment(attachmentRequest, true);
	}

	@Transactional
	public void deleteAttachmentGroupById(String id) {
		AttachmentGroup attachmentGroup = throwIfAttachmentGroupNotFoundOrNotEditable(id);

		this.fileManager = businessLogicService.getFileManager(attachmentGroup.getGroupAttachmentType());

		vaultService.clearVaultAttachmentGroupId(MapStructConverter.MAPPER.toVaultDto(attachmentGroup));

		List<Attachment> attachments = attachmentGroup.getAttachments();
		for (Attachment attachment : attachments) {
			deleteAttachmentById(attachment.getId(), attachmentGroup);
		}

		attachmentGroupRepository.delete(attachmentGroup);
		vaultService.deleteReminder(id);
	}

	private void deleteAttachmentById(String id, AttachmentGroup attachmentGroup, boolean forced) {
		if (!forced) {
			throwIfAttachmentGroupNotEditable(attachmentGroup);
		}

		this.fileManager = businessLogicService.getFileManager(attachmentGroup.getGroupAttachmentType());

		Attachment existing = attachmentRepository.findById(id).orElseThrow(ExceptionPredicate.attachmentNotFound(id));
		removeFile(existing);
		attachmentRepository.delete(existing);
	}

	@Transactional
	@Override
	public void deleteAttachmentById(String id, AttachmentGroup attachmentGroup) {
		deleteAttachmentById(id, attachmentGroup, false);
	}

	@Transactional
	@Override
	public void forceDeleteAttachmentById(String id, AttachmentGroup attachmentGroup) {
		deleteAttachmentById(id, attachmentGroup, true);
	}

	@Override
	public AttachmentGroupDTO findAttachmentGroupByUserIdAndAttachmentGroupId(String id) {
		AttachmentGroup attachmentGroup = attachmentGroupRepository.findByIdAndUserId(id, SecurityUtil.currentUserId())
				.orElseThrow(ExceptionPredicate.attachmentGroupNotFound(id));
		return objectMapper.convertValue(attachmentGroup, AttachmentGroupDTO.class);
	}

	@Transactional
	@Override
	public AdvertisementDTO handleAdvertisementAttachment(
			AdvertisementAttachmentGroupDTO advertisementAttachmentGroupDTO) {
		this.fileManager = businessLogicService.getFileManager(advertisementAttachmentGroupDTO.getType());

		String attachmentBasePathBuilder = businessLogicService
				.defineAttachmentPathPrefix(advertisementAttachmentGroupDTO.getType(), null);
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (advertisementAttachmentGroupDTO.getPath() != null
					&& !advertisementAttachmentGroupDTO.getPath().isEmpty()) {
				advertisementAttachmentGroupDTO
						.setPath(buildPath(attachmentBasePathBuilder) + advertisementAttachmentGroupDTO.getPath());
			}
			else {
				advertisementAttachmentGroupDTO.setPath(attachmentBasePathBuilder);
			}
		}

		saveAdvertisementAttachment(advertisementAttachmentGroupDTO);
		AdvertisementDTO advertisementDTO = new AdvertisementDTO();
		advertisementDTO.setAttachmentGroupId(advertisementAttachmentGroupDTO.getAttachmentGroupId());

		String method = businessLogicService.getAttachmentMethod(advertisementAttachmentGroupDTO.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(advertisementAttachmentGroupDTO.getPath());
		advertisementAttachmentGroupDTO.setPath(responsePath.toString());

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getBannerImage())) {
			advertisementAttachmentGroupDTO.getBannerImage().forEach(advertisementAttachmentDTO -> {
				StringBuilder url = new StringBuilder();
				if (advertisementAttachmentGroupDTO.getPath() != null) {
					url.append(advertisementAttachmentGroupDTO.getPath() + File.separator);
				}
				url.append(LocalFileUtil.formatActualFile(advertisementAttachmentDTO.getId(),
						advertisementAttachmentDTO.getName()));
				advertisementDTO.setBannerImage(url.toString());
			});
		}

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageEn())) {
			advertisementAttachmentGroupDTO.getImageEn().forEach(advertisementAttachmentDTO -> {
				advertisementDTO.setImageEn(buildUrl(advertisementAttachmentDTO.getId(),
						advertisementAttachmentDTO.getName(), advertisementAttachmentGroupDTO.getPath()));
			});
		}

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageCn())) {
			advertisementAttachmentGroupDTO.getImageCn().forEach(advertisementAttachmentDTO -> {
				advertisementDTO.setImageCn(buildUrl(advertisementAttachmentDTO.getId(),
						advertisementAttachmentDTO.getName(), advertisementAttachmentGroupDTO.getPath()));
			});
		}

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageMy())) {
			advertisementAttachmentGroupDTO.getImageMy().forEach(advertisementAttachmentDTO -> {
				advertisementDTO.setImageMy(buildUrl(advertisementAttachmentDTO.getId(),
						advertisementAttachmentDTO.getName(), advertisementAttachmentGroupDTO.getPath()));
			});
		}

		return advertisementDTO;
	}

	@Transactional
	@Override
	public BannerAttachmentResponseDTO handleBannerAttachment(
			BannerAttachmentRequestGroupDTO bannerAttachmentRequestGroupDTO) {
		this.fileManager = businessLogicService.getFileManager(bannerAttachmentRequestGroupDTO.getType());

		String attachmentBasePathBuilder = businessLogicService
				.defineAttachmentPathPrefix(bannerAttachmentRequestGroupDTO.getType(), null);
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (bannerAttachmentRequestGroupDTO.getPath() != null
					&& !bannerAttachmentRequestGroupDTO.getPath().isEmpty()) {
				bannerAttachmentRequestGroupDTO
						.setPath(buildPath(attachmentBasePathBuilder) + bannerAttachmentRequestGroupDTO.getPath());
			}
			else {
				bannerAttachmentRequestGroupDTO.setPath(attachmentBasePathBuilder);
			}
		}

		BannerAttachmentRequestGroupDTO attachmentRequestGroup = saveBannerAttachment(bannerAttachmentRequestGroupDTO);
		BannerAttachmentResponseDTO response = new BannerAttachmentResponseDTO();
		response.setAttachmentGroupId(attachmentRequestGroup.getAttachmentGroupId());

		String method = businessLogicService.getAttachmentMethod(attachmentRequestGroup.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(attachmentRequestGroup.getPath());
		attachmentRequestGroup.setPath(responsePath.toString());

		BannerAttachmentRequestDTO imageEn = attachmentRequestGroup.getImageEn();
		if (Objects.nonNull(imageEn)) {
			response.setImageEnUrl(buildUrl(imageEn.getId(), imageEn.getName(), attachmentRequestGroup.getPath()));
		}

		BannerAttachmentRequestDTO imageMy = attachmentRequestGroup.getImageMy();
		if (Objects.nonNull(imageMy)) {
			response.setImageMyUrl(buildUrl(imageMy.getId(), imageMy.getName(), attachmentRequestGroup.getPath()));
		}

		BannerAttachmentRequestDTO imageCn = attachmentRequestGroup.getImageCn();
		if (Objects.nonNull(imageCn)) {
			response.setImageCnUrl(buildUrl(imageCn.getId(), imageCn.getName(), attachmentRequestGroup.getPath()));
		}

		return response;
	}

	private AdvertisementAttachmentGroupDTO saveAdvertisementAttachment(
			AdvertisementAttachmentGroupDTO advertisementAttachmentGroupDTO) {
		AttachmentGroup attachmentGroupResult = null;
		if (advertisementAttachmentGroupDTO.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(advertisementAttachmentGroupDTO.getClassName(), null,
					advertisementAttachmentGroupDTO.getDescription(), advertisementAttachmentGroupDTO.getName(),
					advertisementAttachmentGroupDTO.getPath(), advertisementAttachmentGroupDTO.getType(), null, null,
					null, null, null, true);
		}
		else {
			attachmentGroupResult = attachmentGroupRepository
					.findById(advertisementAttachmentGroupDTO.getAttachmentGroupId()).orElseThrow(ExceptionPredicate
							.attachmentGroupNotFound(advertisementAttachmentGroupDTO.getAttachmentGroupId()));
			attachmentGroupRepository.save(attachmentGroupResult);
		}
		advertisementAttachmentGroupDTO.setAttachmentGroupId(attachmentGroupResult.getId());

		boolean isEncrypt = AttachmentTypeFileStorageEnum.valueOf(advertisementAttachmentGroupDTO.getType()).getMethod()
				.equals(FileMethod.PRIVATE.getMethod());

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getBannerImage())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(advertisementAttachmentGroupDTO.getBannerImage().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				attachmentEntity.setEncrypt(isEncrypt);
				attachmentEntity.setSecretKey(advertisementAttachmentGroupDTO.getSecretKey());
				return attachmentEntity;
			}).toList());
			advertisementAttachmentGroupDTO.getBannerImage().clear();
			advertisementAttachmentGroupDTO.setBannerImage(MapStructConverter.MAPPER.toAdvertisementAttachmentDTOs(
					this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageEn())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(advertisementAttachmentGroupDTO.getImageEn().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				attachmentEntity.setEncrypt(isEncrypt);
				attachmentEntity.setSecretKey(advertisementAttachmentGroupDTO.getSecretKey());
				return attachmentEntity;
			}).toList());
			advertisementAttachmentGroupDTO.getImageEn().clear();
			advertisementAttachmentGroupDTO.setImageEn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTOs(
					this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageCn())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(advertisementAttachmentGroupDTO.getImageCn().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				attachmentEntity.setEncrypt(isEncrypt);
				attachmentEntity.setSecretKey(advertisementAttachmentGroupDTO.getSecretKey());
				return attachmentEntity;
			}).toList());
			advertisementAttachmentGroupDTO.getImageCn().clear();
			advertisementAttachmentGroupDTO.setImageCn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTOs(
					this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		if (Objects.nonNull(advertisementAttachmentGroupDTO.getImageMy())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(advertisementAttachmentGroupDTO.getImageMy().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				attachmentEntity.setEncrypt(isEncrypt);
				attachmentEntity.setSecretKey(advertisementAttachmentGroupDTO.getSecretKey());
				return attachmentEntity;
			}).toList());
			advertisementAttachmentGroupDTO.getImageMy().clear();
			advertisementAttachmentGroupDTO.setImageMy(MapStructConverter.MAPPER.toAdvertisementAttachmentDTOs(
					this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		return advertisementAttachmentGroupDTO;
	}

	@Transactional
	@Override
	public AdvertisementDetailDTO handleAdvertisementDetailFooterAttachment(
			AdvertisementDetailAttachmentGroupDTO advertisementAttachmentGroupDTO) {
		this.fileManager = businessLogicService.getFileManager(advertisementAttachmentGroupDTO.getType());

		String attachmentBasePathBuilder = businessLogicService
				.defineAttachmentPathPrefix(advertisementAttachmentGroupDTO.getType(), null);
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (advertisementAttachmentGroupDTO.getPath() != null
					&& !advertisementAttachmentGroupDTO.getPath().isEmpty()) {
				advertisementAttachmentGroupDTO
						.setPath(buildPath(attachmentBasePathBuilder) + advertisementAttachmentGroupDTO.getPath());
			}
			else {
				advertisementAttachmentGroupDTO.setPath(attachmentBasePathBuilder);
			}
		}

		saveAdvertisementDetailFooterAttachment(advertisementAttachmentGroupDTO);
		AdvertisementDetailDTO advertisementDetailDTO = new AdvertisementDetailDTO();
		advertisementDetailDTO.setAttachmentGroupId(advertisementAttachmentGroupDTO.getAttachmentGroupId());

		String method = businessLogicService.getAttachmentMethod(advertisementAttachmentGroupDTO.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(advertisementAttachmentGroupDTO.getPath());
		advertisementAttachmentGroupDTO.setPath(responsePath.toString());

		if (Objects.nonNull(advertisementAttachmentGroupDTO.getFooterImage())) {
			advertisementAttachmentGroupDTO.getFooterImage().forEach(advertisementAttachmentDTO -> {
				StringBuilder url = new StringBuilder();
				if (advertisementAttachmentGroupDTO.getPath() != null) {
					url.append(advertisementAttachmentGroupDTO.getPath() + File.separator);
				}
				url.append(LocalFileUtil.formatActualFile(advertisementAttachmentDTO.getId(),
						advertisementAttachmentDTO.getName()));
				advertisementDetailDTO.setFooterImage(url.toString());
			});
		}

		return advertisementDetailDTO;
	}

	private AdvertisementDetailAttachmentGroupDTO saveAdvertisementDetailFooterAttachment(
			AdvertisementDetailAttachmentGroupDTO advertisementDetailAttachmentGroupDTO) {
		AttachmentGroup attachmentGroupResult = null;
		if (advertisementDetailAttachmentGroupDTO.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(advertisementDetailAttachmentGroupDTO.getClassName(),
					null, advertisementDetailAttachmentGroupDTO.getDescription(),
					advertisementDetailAttachmentGroupDTO.getName(), advertisementDetailAttachmentGroupDTO.getPath(),
					advertisementDetailAttachmentGroupDTO.getType(), null, null, null, null, null, true);
		}
		else {
			attachmentGroupResult = attachmentGroupRepository
					.findById(advertisementDetailAttachmentGroupDTO.getAttachmentGroupId())
					.orElseThrow(ExceptionPredicate
							.attachmentGroupNotFound(advertisementDetailAttachmentGroupDTO.getAttachmentGroupId()));
			attachmentGroupRepository.save(attachmentGroupResult);
		}
		advertisementDetailAttachmentGroupDTO.setAttachmentGroupId(attachmentGroupResult.getId());

		boolean isEncrypt = AttachmentTypeFileStorageEnum.valueOf(advertisementDetailAttachmentGroupDTO.getType())
				.getMethod().equals(FileMethod.PRIVATE.getMethod());

		if (Objects.nonNull(advertisementDetailAttachmentGroupDTO.getFooterImage())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(advertisementDetailAttachmentGroupDTO.getFooterImage().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				attachmentEntity.setEncrypt(isEncrypt);
				attachmentEntity.setSecretKey(advertisementDetailAttachmentGroupDTO.getSecretKey());
				return attachmentEntity;
			}).toList());
			advertisementDetailAttachmentGroupDTO.getFooterImage().clear();
			advertisementDetailAttachmentGroupDTO
					.setFooterImage(MapStructConverter.MAPPER.toAdvertisementDetailAttachmentDTOs(
							this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		return advertisementDetailAttachmentGroupDTO;
	}

	private BannerAttachmentRequestGroupDTO saveBannerAttachment(
			BannerAttachmentRequestGroupDTO bannerAttachmentRequestGroupDTO) {
		AttachmentGroup attachmentGroupResult = null;
		if (bannerAttachmentRequestGroupDTO.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(bannerAttachmentRequestGroupDTO.getClassName(), null,
					null, bannerAttachmentRequestGroupDTO.getName(), bannerAttachmentRequestGroupDTO.getPath(),
					bannerAttachmentRequestGroupDTO.getType(), null, null, null, null, null, true);
			bannerAttachmentRequestGroupDTO.setAttachmentGroupId(attachmentGroupResult.getId());
		}
		else {
			attachmentGroupResult = attachmentGroupRepository
					.findById(bannerAttachmentRequestGroupDTO.getAttachmentGroupId()).orElseThrow(ExceptionPredicate
							.attachmentGroupNotFound(bannerAttachmentRequestGroupDTO.getAttachmentGroupId()));
			attachmentGroupRepository.save(attachmentGroupResult);
		}

		boolean isEncrypt = AttachmentTypeFileStorageEnum.valueOf(bannerAttachmentRequestGroupDTO.getType()).getMethod()
				.equals(FileMethod.PRIVATE.getMethod());

		BannerAttachmentRequestDTO imageEn = bannerAttachmentRequestGroupDTO.getImageEn();
		if (Objects.nonNull(imageEn)) {
			imageEn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(imageEn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(BannerAttachmentDescriptionTypeEnum.ENGLISH.getDescription());
			attachmentEntity = saveAndDeletePreviousAttachment(attachmentGroupResult, attachmentEntity,
					BannerAttachmentDescriptionTypeEnum.ENGLISH);
			bannerAttachmentRequestGroupDTO
					.setImageEn(MapStructConverter.MAPPER.toBannerAttachmentRequestDTO(attachmentEntity));
		}

		BannerAttachmentRequestDTO imageMy = bannerAttachmentRequestGroupDTO.getImageMy();
		if (Objects.nonNull(imageMy)) {
			imageMy.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(imageMy, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(BannerAttachmentDescriptionTypeEnum.MALAY.getDescription());
			attachmentEntity = saveAndDeletePreviousAttachment(attachmentGroupResult, attachmentEntity,
					BannerAttachmentDescriptionTypeEnum.MALAY);
			bannerAttachmentRequestGroupDTO
					.setImageMy(MapStructConverter.MAPPER.toBannerAttachmentRequestDTO(attachmentEntity));
		}

		BannerAttachmentRequestDTO imageCn = bannerAttachmentRequestGroupDTO.getImageCn();
		if (Objects.nonNull(imageCn)) {
			imageCn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(imageCn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(BannerAttachmentDescriptionTypeEnum.CHINESE.getDescription());
			attachmentEntity = saveAndDeletePreviousAttachment(attachmentGroupResult, attachmentEntity,
					BannerAttachmentDescriptionTypeEnum.CHINESE);
			bannerAttachmentRequestGroupDTO
					.setImageCn(MapStructConverter.MAPPER.toBannerAttachmentRequestDTO(attachmentEntity));
		}

		return bannerAttachmentRequestGroupDTO;
	}

	private String buildUrl(String id, String name, String attachmentGroupPath) {
		StringBuilder url = new StringBuilder();
		if (attachmentGroupPath != null) {
			url.append(attachmentGroupPath).append(File.separator);
		}
		url.append(LocalFileUtil.formatActualFile(id, name));
		return url.toString();
	}

	@Transactional
	@Override
	public BankListDTO handleBankListAttachment(BankListAttachmentRequestGroupDTO bankListAttachmentRequestGroupDTO,
			BankImageType bankImageType) {
		this.fileManager = businessLogicService.getFileManager(bankListAttachmentRequestGroupDTO.getType());

		String attachmentBasePathBuilder = businessLogicService
				.defineAttachmentPathPrefix(bankListAttachmentRequestGroupDTO.getType(), null);
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (bankListAttachmentRequestGroupDTO.getPath() != null
					&& !bankListAttachmentRequestGroupDTO.getPath().isEmpty()) {
				bankListAttachmentRequestGroupDTO
						.setPath(buildPath(attachmentBasePathBuilder) + bankListAttachmentRequestGroupDTO.getPath());
			}
			else {
				bankListAttachmentRequestGroupDTO.setPath(attachmentBasePathBuilder);
			}
		}

		saveBankListAttachment(bankListAttachmentRequestGroupDTO);
		BankListDTO bankListDTO = new BankListDTO();
		String method = businessLogicService.getAttachmentMethod(bankListAttachmentRequestGroupDTO.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(bankListAttachmentRequestGroupDTO.getPath());
		bankListAttachmentRequestGroupDTO.setPath(responsePath.toString());
		if (Objects.nonNull(bankListAttachmentRequestGroupDTO.getBankImage())) {
			bankListAttachmentRequestGroupDTO.getBankImage().forEach(bankListAttachmentRequestDTO -> {
				StringBuilder url = new StringBuilder();
				if (bankListAttachmentRequestGroupDTO.getPath() != null) {
					url.append(bankListAttachmentRequestGroupDTO.getPath() + File.separator);
				}
				url.append(LocalFileUtil.formatActualFile(bankListAttachmentRequestDTO.getId(),
						bankListAttachmentRequestDTO.getName()));
				if (bankImageType.equals(BankImageType.LOGO)) {
					bankListDTO.setAttachmentGroupId(bankListAttachmentRequestGroupDTO.getAttachmentGroupId());
					bankListDTO.setUrl(url.toString());
				}
				else if (bankImageType.equals(BankImageType.ICON)) {
					bankListDTO.setIconAttachmentGroupId(bankListAttachmentRequestGroupDTO.getAttachmentGroupId());
					bankListDTO.setIconUrl(url.toString());
				}
			});
		}

		return bankListDTO;
	}

	private BankListAttachmentRequestGroupDTO saveBankListAttachment(
			BankListAttachmentRequestGroupDTO bankListAttachmentRequestGroupDTO) {
		AttachmentGroup attachmentGroupResult;
		if (bankListAttachmentRequestGroupDTO.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(bankListAttachmentRequestGroupDTO.getClassName(), null,
					bankListAttachmentRequestGroupDTO.getDescription(), bankListAttachmentRequestGroupDTO.getName(),
					bankListAttachmentRequestGroupDTO.getPath(), bankListAttachmentRequestGroupDTO.getType(), null,
					null, null, null, null, true);
		}
		else {
			attachmentGroupResult = attachmentGroupRepository
					.findById(bankListAttachmentRequestGroupDTO.getAttachmentGroupId()).orElseThrow(ExceptionPredicate
							.attachmentGroupNotFound(bankListAttachmentRequestGroupDTO.getAttachmentGroupId()));
			attachmentGroupRepository.save(attachmentGroupResult);
		}
		bankListAttachmentRequestGroupDTO.setAttachmentGroupId(attachmentGroupResult.getId());

		if (Objects.nonNull(bankListAttachmentRequestGroupDTO.getBankImage())) {
			List<Attachment> inputAttachments = new ArrayList<>();
			inputAttachments.addAll(bankListAttachmentRequestGroupDTO.getBankImage().stream().map(attachment -> {
				attachment.setType(AttachmentTypeEnum.Image.name());
				Attachment attachmentEntity = objectMapper.convertValue(attachment, Attachment.class);
				return attachmentEntity;
			}).toList());
			bankListAttachmentRequestGroupDTO.getBankImage().clear();
			bankListAttachmentRequestGroupDTO
					.setBankImage(MapStructConverter.MAPPER.toBankListAttachmentRequestGroupDTOs(
							this.saveAttachment(attachmentGroupResult, new HashSet<>(inputAttachments), null)));
		}
		return bankListAttachmentRequestGroupDTO;
	}

	@Override
	public AttachmentGroup throwIfAttachmentGroupNotEditable(@NonNull AttachmentGroup attachmentGroup) {
		if (Boolean.FALSE.equals(attachmentGroup.getIsEditable())) {
			throw new BusinessException(ErrorCodeEnum.VAULT_NOT_EDITABLE);
		}

		return attachmentGroup;
	}

	@Override
	public AttachmentGroup throwIfAttachmentGroupNotFoundOrNotEditable(@NonNull String id) {
		AttachmentGroup attachmentGroup = attachmentGroupRepository.findById(id)
				.orElseThrow(ExceptionPredicate.attachmentGroupNotFound(id));
		return throwIfAttachmentGroupNotEditable(attachmentGroup);
	}

	@Override
	public List<Attachment> findEkycAttachmentsByAttachmentGroupId(String attachmentGroupId) {
		return attachmentRepository.findEkycAttachmentsByAttachmentGroupId(attachmentGroupId);
	}

	@Override
	@Transactional
	public AdvertisementAttachmentPopUpResponseDTO handlePopUpImage(AdvertisementAttachmentPopUpGroupDTO request) {
		this.fileManager = businessLogicService.getFileManager(request.getType());

		String attachmentBasePathBuilder = businessLogicService.defineAttachmentPathPrefix(request.getType(), null);
		if (!attachmentBasePathBuilder.isEmpty()) {
			if (request.getPath() != null && !request.getPath().isEmpty()) {
				request.setPath(buildPath(attachmentBasePathBuilder) + request.getPath());
			}
			else {
				request.setPath(attachmentBasePathBuilder);
			}
		}

		AdvertisementAttachmentPopUpGroupDTO advertisementAttachmentPopUpGroupDTO = saveAdvertisementAttachmentPopUp(
				request);
		AdvertisementAttachmentPopUpResponseDTO response = new AdvertisementAttachmentPopUpResponseDTO();
		response.setAttachmentGroupId(request.getAttachmentGroupId());

		String method = businessLogicService.getAttachmentMethod(request.getType());
		StringBuilder responsePath = new StringBuilder(baseProperties.getCdn().getHost()).append(File.separator);
		if (method.equals(FileMethod.PUBLIC.getMethod())) {
			responsePath.append(method).append(File.separator);
		}
		responsePath.append(request.getPath());
		request.setPath(responsePath.toString());

		AdvertisementAttachmentDTO popUpEn = advertisementAttachmentPopUpGroupDTO.getPopUpImageEn();
		if (Objects.nonNull(popUpEn)) {
			response.setPopUpImageEnUrl(
					buildUrl(popUpEn.getId(), popUpEn.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}

		AdvertisementAttachmentDTO popUpCn = advertisementAttachmentPopUpGroupDTO.getPopUpImageCn();
		if (Objects.nonNull(popUpCn)) {
			response.setPopUpImageCnUrl(
					buildUrl(popUpCn.getId(), popUpCn.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}

		AdvertisementAttachmentDTO popUpMy = advertisementAttachmentPopUpGroupDTO.getPopUpImageMy();
		if (Objects.nonNull(popUpMy)) {
			response.setPopUpImageMyUrl(
					buildUrl(popUpMy.getId(), popUpMy.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}
		AdvertisementAttachmentDTO imageMy = advertisementAttachmentPopUpGroupDTO.getFullImageMy();
		if (Objects.nonNull(imageMy)) {
			response.setFullImageMyUrl(
					buildUrl(imageMy.getId(), imageMy.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}
		AdvertisementAttachmentDTO imageEn = advertisementAttachmentPopUpGroupDTO.getFullImageEn();
		if (Objects.nonNull(imageEn)) {
			response.setFullImageEnUrl(
					buildUrl(imageEn.getId(), imageEn.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}
		AdvertisementAttachmentDTO imageCn = advertisementAttachmentPopUpGroupDTO.getFullImageCn();
		if (Objects.nonNull(imageCn)) {
			response.setFullImageCnUrl(
					buildUrl(imageCn.getId(), imageCn.getName(), advertisementAttachmentPopUpGroupDTO.getPath()));
		}
		return response;

	}

	private AdvertisementAttachmentPopUpGroupDTO saveAdvertisementAttachmentPopUp(
			AdvertisementAttachmentPopUpGroupDTO popUpGroupDTO) {
		AttachmentGroup attachmentGroupResult = null;
		if (popUpGroupDTO.getAttachmentGroupId() == null) {
			attachmentGroupResult = this.createAttachmentGroup(popUpGroupDTO.getClassName(), null,
					popUpGroupDTO.getDescription(), popUpGroupDTO.getName(), popUpGroupDTO.getPath(),
					popUpGroupDTO.getType(), null, null, null, null, null, true);
		}
		else {
			attachmentGroupResult = attachmentGroupRepository.findById(popUpGroupDTO.getAttachmentGroupId())
					.orElseThrow(ExceptionPredicate.attachmentGroupNotFound(popUpGroupDTO.getAttachmentGroupId()));
			attachmentGroupRepository.save(attachmentGroupResult);
		}
		popUpGroupDTO.setAttachmentGroupId(attachmentGroupResult.getId());

		boolean isEncrypt = AttachmentTypeFileStorageEnum.valueOf(popUpGroupDTO.getType()).getMethod()
				.equals(FileMethod.PRIVATE.getMethod());

		AdvertisementAttachmentDTO popUpEn = popUpGroupDTO.getPopUpImageEn();
		if (popUpEn != null) {
			popUpEn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(popUpEn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(popUpEn.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setPopUpImageEn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}

		AdvertisementAttachmentDTO popUpMy = popUpGroupDTO.getPopUpImageMy();
		if (popUpMy != null) {
			popUpMy.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(popUpMy, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(popUpMy.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setPopUpImageMy(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}

		AdvertisementAttachmentDTO popUpCn = popUpGroupDTO.getPopUpImageCn();
		if (popUpCn != null) {
			popUpCn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(popUpCn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(popUpCn.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setPopUpImageCn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}
		AdvertisementAttachmentDTO fullImageMy = popUpGroupDTO.getFullImageMy();
		if (fullImageMy != null) {
			fullImageMy.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(fullImageMy, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(fullImageMy.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setFullImageMy(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}

		AdvertisementAttachmentDTO fullImageCn = popUpGroupDTO.getFullImageCn();
		if (fullImageCn != null) {
			fullImageCn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(fullImageCn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(fullImageCn.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setFullImageCn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}
		AdvertisementAttachmentDTO fullImageEn = popUpGroupDTO.getFullImageEn();
		if (fullImageEn != null) {
			fullImageEn.setType(AttachmentTypeEnum.Image.name());
			Attachment attachmentEntity = objectMapper.convertValue(fullImageEn, Attachment.class);
			attachmentEntity.setEncrypt(isEncrypt);
			attachmentEntity.setDescription(fullImageEn.getDescription());
			HashSet<Attachment> attachments = new HashSet<>(Collections.singleton(attachmentEntity));
			attachmentEntity = this.saveAttachment(attachmentGroupResult, attachments, null).get(0);
			popUpGroupDTO.setFullImageEn(MapStructConverter.MAPPER.toAdvertisementAttachmentDTO(attachmentEntity));
		}

		return popUpGroupDTO;
	}

	@Transactional
	@Override
	public List<String> rewriteVaultsForUserInterest(RewriteVaultDTO data) throws IOException {
		List<String> result = new ArrayList<>();
		for (String vaultId : data.getVaultIds()) {
			Optional<AttachmentGroup> attachmentGroup = attachmentGroupRepository.findByIdAndUserId(vaultId,
					data.getUserId());
			if (attachmentGroup.isEmpty()) {
				continue;
			}

			String response = rewriteVault(attachmentGroup.get(), data);
			result.add(response);
		}
		return result;
	}

	@Transactional
	@Override
	public List<String> rewriteVaultsForUserInterestPublic(RewriteVaultDTO data) throws IOException {
		List<String> result = new ArrayList<>();
		for (String vaultId : data.getVaultIds()) {
			Optional<AttachmentGroup> attachmentGroup = attachmentGroupRepository.findByIdAndPublicKeyId(vaultId,
					data.getPublicId());
			if (attachmentGroup.isEmpty()) {
				continue;
			}

			String response = rewriteVault(attachmentGroup.get(), data);
			result.add(response);
		}
		return result;
	}

	private String rewriteVault(AttachmentGroup attachmentGroup, RewriteVaultDTO data) throws IOException {
		AttachmentGroup replicateGroup = objectMapper.convertValue(attachmentGroup, AttachmentGroup.class);
		replicateGroup.setId(null);
		replicateGroup.setUserId(null);
		replicateGroup.setPublicKeyId(null);
		replicateGroup = attachmentGroupRepository.save(replicateGroup);

		this.fileManager = businessLogicService.getFileManager(attachmentGroup.getGroupAttachmentType());
		List<Attachment> attachments = attachmentGroup.getAttachments();
		for (Attachment attachment : attachments) {
			Attachment replicateAttachment = objectMapper.convertValue(attachment, Attachment.class);

			if (attachment.isTemp()) {
				attachment.setTemp(false);
				attachmentRepository.save(attachment);
			}

			replicateAttachment.setId(null);
			if (StringUtils.isNotBlank(data.getAdditionalPrefix())) {
				replicateAttachment.setName(data.getAdditionalPrefix() + replicateAttachment.getName());
			}
			replicateAttachment.setAttachmentGroup(replicateGroup);
			replicateAttachment.setTemp(false);
			replicateAttachment = attachmentRepository.save(replicateAttachment);
			log.info("folder file: {}, {}, {}", attachmentGroup.getBasePath(), attachment.getId(),
					attachment.getName());

			OutputContentFile outputContentFile = fileManager.getFile(attachmentGroup.getBasePath(), attachment.getId(),
					attachment.getName());
			ByteArraySecret input = new ByteArraySecret(outputContentFile.getFile().toByteArray(),
					data.getSecretKeySource());
			ByteArraySecret output;
			try {
				output = byteArraySecretCrypto.decrypt(input);
			}
			catch (CryptoException e) {
				log.error("error decrypt due to:", e);
				throw new AccessDeniedException("Failed to decrypt");
			}

			// rewrite use institution secret key
			InputContentFile inputContentFile = new InputContentFile();
			inputContentFile.setFileName(replicateAttachment.getName());
			inputContentFile.setPath(replicateGroup.getBasePath());
			byte[] byteArray = output.getData();

			ByteArraySecret newInputFile = new ByteArraySecret(byteArray, data.getSecretKeyDestination());
			ByteArraySecret newOutputFile;

			try {
				newOutputFile = byteArraySecretCrypto.encrypt(newInputFile);
			}
			catch (CryptoException e) {
				log.error("Encryption error: {}", e.getMessage());
				throw new CommonServiceException("Failed to encrypt");
			}
			inputContentFile.setFile(newOutputFile.getData());
			inputContentFile.setKey(replicateAttachment.getId());
			this.fileManager.addFile(inputContentFile);
		}
		return replicateGroup.getId();
	}

	@Override
	@Transactional
	public void deleteTemporaryVault() {
		Instant cutOffTime = Instant.now().minus(1, ChronoUnit.DAYS);
		List<Attachment> unusedVault = attachmentRepository.findAllTemporaryFilesPassedOneDay(cutOffTime);
		if (unusedVault.isEmpty()) {
			log.info("there is no unused vault at: {}", cutOffTime);
			return;
		}
		Set<AttachmentGroup> attachmentGroups = new HashSet<>();
		for (Attachment attachment : unusedVault) {
			try {
				removeFile(attachment);
			}
			catch (Exception e) {
				log.error("Failed to remove temporary file: {}", attachment);
			}
			attachmentGroups.add(attachment.getAttachmentGroup());
			attachmentRepository.delete(attachment);
		}
		attachmentGroupRepository.deleteAll(attachmentGroups);
	}

}
