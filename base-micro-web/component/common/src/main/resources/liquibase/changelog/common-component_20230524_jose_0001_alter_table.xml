<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.19.xsd">
    <changeSet id="common-component_20230524_jose_0001" author="Jose <PERSON>">
        <addColumn tableName="user_journey" schemaName="">
            <column name="flow_type" type="VARCHAR(11)"/>
        </addColumn>
        <addColumn tableName="journey_configuration" schemaName="">
            <column name="do_manual_complete_checking" type="BOOLEAN"/>
        </addColumn>
        <sql>
            UPDATE journey_configuration SET do_manual_complete_checking = false;
            UPDATE journey_configuration SET do_manual_complete_checking = true WHERE id = 'ba330141-c69e-4a29-88a9-68803a821cd1';
        </sql>
    </changeSet>
</databaseChangeLog>