package my.com.mandrill.component.service;

import jakarta.persistence.EntityNotFoundException;
import my.com.mandrill.component.domain.Postcode;
import my.com.mandrill.component.repository.jpa.PostcodeRepository;
import my.com.mandrill.component.service.impl.PostcodeServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
public class PostcodeServiceTest {

	@InjectMocks
	PostcodeServiceImpl postcodeService;

	@Mock
	PostcodeRepository postcodeRepository;

	List<Postcode> mockResult;

	@BeforeEach
	void setup() {
		mockResult = new ArrayList<>();
		for (int i = 0; i < 5; i++) {
			Postcode postcode = new Postcode();
			postcode.setId(UUID.randomUUID().toString());
			postcode.setPostcode(String.format("postcode%d", i));
			mockResult.add(postcode);
		}
	}

	@Test
	void findByStateId_ShouldReturnPostcodes_Positive() {
		Mockito.when(postcodeRepository.findByStateId(eq("stateId"), any(Sort.class))).thenReturn(mockResult);

		List<Postcode> postcodes = postcodeService.findByStateId(Sort.unsorted(), "stateId");
		Assertions.assertEquals(mockResult.size(), postcodes.size());
	}

	@Test
	void getPostcodeById_GivenExistingId_ShouldReturnPostcode_Positive() {
		Postcode postcode = mockResult.get(0);
		Mockito.when(postcodeRepository.findById(postcode.getId())).thenReturn(Optional.of(postcode));

		Postcode result = postcodeService.getPostcodeById(postcode.getId());
		Assertions.assertEquals(postcode, result);
	}

	@Test
	void getPostcodeById_GivenNonExistingId_ShouldThrowNotFoundException_Negative() {
		String id = UUID.randomUUID().toString();
		Mockito.when(postcodeRepository.findById(id)).thenReturn(Optional.empty());
		Assertions.assertThrows(EntityNotFoundException.class, () -> postcodeService.getPostcodeById(id));
	}

	@Test
	void getPostcodeByCode_GivenExistingCode_ShouldReturnPostcode_Positive() {
		Postcode postcode = mockResult.get(0);
		Mockito.when(postcodeRepository.findByPostcode(postcode.getPostcode())).thenReturn(Optional.of(postcode));

		Postcode result = postcodeService.getPostcodeByCode(postcode.getPostcode());
		Assertions.assertEquals(postcode, result);
	}

	@Test
	void getPostcodeByCode_GivenNonExistingCode_ShouldThrowNotFoundException_Negative() {
		String postcode = "nonExistingCode";
		Mockito.when(postcodeRepository.findByPostcode(postcode)).thenReturn(Optional.empty());
		Assertions.assertThrows(EntityNotFoundException.class, () -> postcodeService.getPostcodeByCode(postcode));
	}

}