package my.com.mandrill.document.service;

import my.com.mandrill.document.dto.model.DocumentOutput;
import my.com.mandrill.utilities.general.constant.PointWithdrawalDateType;

import java.io.IOException;
import java.time.LocalDate;

public interface PointsWithdrawalReportService {

	DocumentOutput generatePointsWithdrawalReport(String search, String status, PointWithdrawalDateType dateType,
			LocalDate dateFrom, LocalDate dateTo) throws IOException;

}
