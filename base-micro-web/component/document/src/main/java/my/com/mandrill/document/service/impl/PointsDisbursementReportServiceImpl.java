package my.com.mandrill.document.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.document.config.BaseProperties;
import my.com.mandrill.document.dto.model.DocumentOutput;
import my.com.mandrill.document.exception.DocumentComponentException;
import my.com.mandrill.document.service.PointsDisbursementReportService;
import my.com.mandrill.utilities.feign.client.MoneyXCoreFeignClient;
import my.com.mandrill.utilities.feign.dto.model.PointsDisbursementExportDTO;
import my.com.mandrill.utilities.general.constant.DocumentType;
import my.com.mandrill.utilities.general.constant.PointDisbursementDateType;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointsDisbursementReportServiceImpl extends JasperReportService
		implements PointsDisbursementReportService {

	private final BaseProperties baseProperties;

	private final MoneyXCoreFeignClient moneyXCoreFeignClient;

	@Override
	public DocumentOutput generatePointsDisbursementReport(String search, RSMRelationType rsmScenario,
			String rsmFocalType, PointEarningStatus status, PointDisbursementDateType dateType, LocalDate dateFrom,
			LocalDate dateTo) throws IOException {
		DocumentType documentType = DocumentType.XLSX;
		File file = Paths.get(getDirectory(), documentType.getRandomFileName()).toFile();
		try {
			List<PointsDisbursementExportDTO> data = moneyXCoreFeignClient.getAllPointDisbursementsForExport(search,
					rsmScenario, rsmFocalType, status, dateType, dateFrom, dateTo);

			JasperReport jasperReport = JasperCompileManager.compileReport(pointsDisbursementReport.getInputStream());
			List<JasperPrint> prints = new ArrayList<>();
			Map<String, Object> parameters = new HashMap<>();
			parameters.put("POINTS_DISBURSEMENT_DATA", new JRBeanCollectionDataSource(data));

			JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new JREmptyDataSource());
			prints.add(jasperPrint);
			return new DocumentOutput(exportXlsxReport(file, new String[] { "Points Disbursement" }, prints),
					"Points_Disbursement_Report".concat(documentType.getExtension()), documentType);
		}
		catch (Exception e) {
			String msg = String.format("Failed to export points disbursement %s", file.getName());
			log.error(msg, e);
			throw new DocumentComponentException(msg, e);
		}
	}

	@Override
	public String getDirectory() throws IOException {
		Path path = Paths.get(baseProperties.getReport().getPath().getGenerated());
		if (Files.notExists(path)) {
			Files.createDirectories(path);
		}
		return path.toString();
	}

}
