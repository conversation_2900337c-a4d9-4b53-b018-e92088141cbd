package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.primary.JobStatus;
import my.com.mandrill.component.dto.model.JobStatusDTO;
import my.com.mandrill.component.dto.request.JobStatusRequest;
import my.com.mandrill.component.service.JobStatusService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Tag(name = "job")
@Slf4j
@RestController
@RequestMapping("/jobs")
@RequiredArgsConstructor
public class JobController {

	private final JobStatusService jobStatusService;

	@SecurityRequirements
	@PostMapping("/status")
	@ResponseStatus(HttpStatus.ACCEPTED)
	public ResponseEntity<JobStatusDTO> processJobStatus(@RequestBody @Valid JobStatusRequest jobStatusRequest) {
		return ResponseEntity.accepted()
				.body(MapStructConverter.MAPPER.toJobStatusDTO(jobStatusService.processJobStatus(jobStatusRequest)));
	}

	@GetMapping("/status")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<JobStatusDTO>> findJobStatus(@RequestParam(required = false) String jobId) {
		List<JobStatus> jobStatuses = new ArrayList<>();
		if (StringUtils.isNotBlank(jobId)) {
			jobStatuses.add(jobStatusService.findByJobId(jobId));
		}
		else {
			jobStatuses = jobStatusService.findByUserId(SecurityUtil.currentUserId());
		}

		return ResponseEntity.ok(jobStatuses.stream().map(MapStructConverter.MAPPER::toJobStatusDTO).toList());
	}

	@Hidden
	@PostMapping("/integration/status")
	public ResponseEntity<JobStatusDTO> processIntegrationJobStatus(@RequestBody JobStatusRequest jobStatusRequest) {
		return ResponseEntity.ok(MapStructConverter.MAPPER
				.toJobStatusDTO(jobStatusService.processIntegrationJobStatus(jobStatusRequest)));
	}

}
