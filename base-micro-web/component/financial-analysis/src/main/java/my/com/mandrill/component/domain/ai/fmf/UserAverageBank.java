package my.com.mandrill.component.domain.ai.fmf;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "user_average_bank", schema = "fmf")
public class UserAverageBank {

	@Id
	@Column(name = "id")
	private String id;

	@Column(name = "user_id")
	private String userId;

	@Column(name = "bank_name")
	private String bankName;

	@Column(name = "issuer_code")
	private String issuerCode;

	@Column(name = "total_credit")
	private BigDecimal totalCredit;

	@Column(name = "total_months")
	private Integer totalMonths;

	@Column(name = "average_income")
	private BigDecimal averageIncome;

	@Column(name = "created_at")
	private Instant createdAt;

	@Column(name = "updated_at")
	private Instant updatedAt;

	@Column(name = "deleted_at")
	private Instant deletedAt;

}
