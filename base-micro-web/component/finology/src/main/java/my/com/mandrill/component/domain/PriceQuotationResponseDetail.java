package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "price_quotation_response_detail")
public class PriceQuotationResponseDetail extends AuditSection {

	@NotNull
	@ManyToOne(optional = false)
	@JoinColumn(name = "price_quotation_response_id", nullable = false)
	private PriceQuotationResponse priceQuotationResponse;

	@Column(name = "response_code")
	private Integer responseCode;

	@Column(name = "quotation_no")
	private String quotationNo;

	@Column(name = "insurer_code")
	private String insurerCode;

	@Column(name = "insurer_sum_insured_value")
	private String insurerSumInsuredValue;

	@Column(name = "general_price")
	private String generalPrice;

	@Column(name = "ncd_details")
	private String ncdDetails;

	@Column(name = "extra_coverage")
	private String extraCoverage;

	@Column(name = "covernote_allowed")
	private Boolean coverNoteAllowed;

	@Column(name = "road_tax_availability")
	private String roadTaxAvailability;

	@Column(name = "road_tax_error_description")
	private String roadTaxErrorDescription;

	@Column(name = "road_tax_amount")
	private BigDecimal roadTaxAmount;

	@Column(name = "delivery_fee")
	private BigDecimal deliveryFee;

	@Column(name = "delivery_sst")
	private BigDecimal deliverySst;

	@Column(name = "service_fee")
	private BigDecimal serviceFee;

	@Column(name = "service_fee_sst")
	private BigDecimal serviceFeeSst;

	@Column(name = "road_tax_total")
	private BigDecimal roadTaxTotal;

	@Transient
	private String refCode;

	@Column(name = "error_code")
	private String errorCode;

	@Column(name = "message")
	private String message;

	@Column(name = "referral_message")
	private String referralMessage;

	@Column(name = "user_id")
	private String userId;

	@Column(name = "transaction_id")
	private String transactionId;

}
