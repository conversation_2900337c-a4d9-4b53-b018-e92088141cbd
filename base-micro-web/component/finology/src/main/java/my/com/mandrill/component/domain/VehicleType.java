package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vehicle_type", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class VehicleType extends AuditSection {

	@NotBlank
	@Size(max = 255)
	@Column(name = "name", nullable = false)
	private String name;

	@NotBlank
	@Size(max = 255)
	@Column(name = "description")
	private String description;

	@NotBlank
	@Size(max = 255)
	@Column(name = "code")
	private String code;

}
