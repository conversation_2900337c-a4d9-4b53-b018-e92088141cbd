package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VixRequestDTO implements Serializable {

	@JsonProperty("ref_code")
	private String refCode;

	@JsonProperty("request_date_time")
	private String requestDateTime;

	@JsonProperty("hash_code")
	private String hashCode;

	@JsonProperty("vehicle_registration_no")
	private String vehicleRegistrationNo;

	@JsonProperty("id_type")
	private String idType;

	@JsonProperty("identification_no")
	private String identificationNo;

	@JsonProperty("postcode")
	private String postcode;

}
