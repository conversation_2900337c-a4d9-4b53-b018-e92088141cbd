package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.ExtraCoverage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExtraCoverageRepository extends JpaRepository<ExtraCoverage, String> {

	Page<ExtraCoverage> findByProviderIdContainsIgnoreCaseAndVehicleTypeNameContainsIgnoreCaseAndCoverTypeCodeContainsIgnoreCase(
			String providerId, String vehicleType, String coverType, Pageable pageable);

	List<ExtraCoverage> findAllByVehicleTypeIdAndProviderIdAndCoverTypeCode(String vehicleTypeId, String providerId,
			String coverTypeCode);

	Optional<ExtraCoverage> findByCodeAndVehicleTypeIdAndProviderIdAndCoverTypeCode(String code, String vehicleTypeId,
			String providerId, String coverTypeCode);

	boolean existsByCodeAndProviderId(String code, String providerId);

	List<ExtraCoverage> findByCode(String code);

}
