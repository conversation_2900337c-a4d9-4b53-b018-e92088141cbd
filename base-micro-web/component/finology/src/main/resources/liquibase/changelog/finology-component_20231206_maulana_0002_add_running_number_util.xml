<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="finology-component_20231206_maulana_0001" author="maulana">
        <createTable tableName="running_number">
            <column name="module_name" type="VARCHAR(100)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_running_number"/>
            </column>
            <column name="running_number" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="prefix" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="%d" name="number_format" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="include_day" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="institution_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addUniqueConstraint columnNames="module_name, institution_id" constraintName="uc_beb280372e3203dd5dbc5fb22"
                             tableName="running_number"/>
        <loadData encoding="UTF-8"
                  file="/liquibase/dataset/running-number/running-number.csv"
                  separator=";"
                  tableName="running_number"/>
    </changeSet>
</databaseChangeLog>