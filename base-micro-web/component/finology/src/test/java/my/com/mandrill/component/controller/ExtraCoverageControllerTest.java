package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import my.com.mandrill.component.domain.ExtraCoverage;
import my.com.mandrill.component.dto.model.ExtraCoverageDTO;
import my.com.mandrill.component.dto.request.ExtraCoverageRequest;
import my.com.mandrill.component.service.ExtraCoverageIntegrationService;
import my.com.mandrill.component.service.ExtraCoverageService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(value = ExtraCoverageController.class, excludeAutoConfiguration = SecurityAutoConfiguration.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = ExtraCoverageController.class)
class ExtraCoverageControllerTest {

	@Autowired
	MockMvc mockMvc;

	@Autowired
	ObjectMapper objectMapper;

	@MockBean
	ExtraCoverageService extraCoverageService;

	@MockBean
	AccountFeignClient accountFeignClient;

	@MockBean
	ExtraCoverageIntegrationService extraCoverageIntegrationService;

	ExtraCoverageDTO extraCoverageDTOMock;

	ExtraCoverage extraCoverageMock;

	List<ExtraCoverage> extraCoverageListMock;

	CurrentUserIdDTO currentUserIdDTOMock;

	ExtraCoverageRequest extraCoverageRequestMock;

	String baseUrl = "/extra-coverage";

	@BeforeEach
	void setup() {
		extraCoverageDTOMock = new ExtraCoverageDTO();
		extraCoverageMock = new ExtraCoverage();
		extraCoverageListMock = new ArrayList<>();
		currentUserIdDTOMock = new CurrentUserIdDTO();
		extraCoverageRequestMock = new ExtraCoverageRequest();
	}

	@Test
	void findAll_GiveValidArgument_Positive() throws Exception {
		extraCoverageListMock.add(extraCoverageMock);
		Page<ExtraCoverage> extraCoveragePage = new PageImpl<>(extraCoverageListMock);
		when(extraCoverageService.findAll(any(), any(), any(), any())).thenReturn(extraCoveragePage);

		mockMvc.perform(MockMvcRequestBuilders.get(baseUrl)).andDo(print()).andExpect(status().isOk())
				.andExpect(jsonPath("$.content", hasSize(1)));
	}

	@Test
	void findAllByVixTransactionCodeAndInsurerCode_GiveValidArgument_Positive() throws Exception {
		extraCoverageRequestMock.setCoverTypeCode("type");
		extraCoverageRequestMock.setVixTransactionCode("vix");
		extraCoverageRequestMock.setInsurerCode("code");
		extraCoverageListMock.add(extraCoverageMock);
		when(accountFeignClient.getCurrentUserId()).thenReturn(currentUserIdDTOMock);
		when(extraCoverageIntegrationService.findAllByInsurerCodeAndVixTransactionCode(any(), any()))
				.thenReturn(extraCoverageListMock);

		mockMvc.perform(
				MockMvcRequestBuilders.post(baseUrl).content(objectMapper.writeValueAsString(extraCoverageRequestMock))
						.contentType(MediaType.APPLICATION_JSON))
				.andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.size()").value(1));
	}

}
