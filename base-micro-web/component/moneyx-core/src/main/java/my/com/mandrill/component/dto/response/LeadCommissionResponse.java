package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.dto.model.LeadCommissionDetailData;
import my.com.mandrill.component.dto.model.LeadPointEarningData;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeadCommissionResponse {

	private String headerId;

	private String commissionId;

	private String name;

	private String rewardName;

	private String productProviderName;

	private String productName;

	private BigDecimal revenue;

	private String remarks;

	private List<LeadCommissionDetailData> details;

	private List<LeadPointEarningData> earnings;

	private boolean confirmed;

	private RSMCommissionType commissionType;

}
