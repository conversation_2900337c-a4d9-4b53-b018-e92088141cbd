package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PointDisbursementPointsEarningDetailResponse {

	private String pointsEarningId;

	private String pointsName;

	private PointEarningStatus pointsEarningStatus;

	private BigDecimal pointsAmount;

	private Instant pointsDisbursementDate;

}
