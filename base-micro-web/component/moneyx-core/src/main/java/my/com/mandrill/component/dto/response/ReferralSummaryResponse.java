package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.dto.model.RefereeEarningData;
import my.com.mandrill.component.dto.model.PointEarningData;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReferralSummaryResponse {

	private int totalFriends;

	private long totalEarnings;

	private List<PointEarningData> earnings;

	private String referralCode;

	private List<RefereeEarningData> referredFriends;

}
