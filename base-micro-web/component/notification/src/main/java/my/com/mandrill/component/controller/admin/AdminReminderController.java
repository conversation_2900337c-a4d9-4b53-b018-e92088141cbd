package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.ReminderMigrateRequest;
import my.com.mandrill.component.service.ReminderIntegrationService;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("admin/reminders")
public class AdminReminderController {

	private final ReminderIntegrationService reminderIntegrationService;

	@PostMapping("migrate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.MIGRATE_REMINDER)")
	public void migrateReminder(@Valid @RequestBody ReminderMigrateRequest migrateRequest) {
		reminderIntegrationService.migrateReminderFrequency(migrateRequest.getReminderType(),
				migrateRequest.getSourceFrequency(), migrateRequest.getDestinationFrequency());
	}

}
