package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.component.constant.Status;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;

@Entity
@Table(name = "push_notification_one_signal")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PushNotificationOneSignal extends AuditSection implements Serializable {

	@Column(name = "one_signal_id")
	private String oneSignalId;

	@Column(name = "status", length = 16, nullable = false)
	@Enumerated(EnumType.STRING)
	private Status status;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonBackReference
	@JoinColumn(name = "push_notification_id", nullable = false)
	private PushNotification pushNotification;

}
