package my.com.mandrill.component.dto.projection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.InboxType;
import my.com.mandrill.utilities.general.constant.ActionType;

import java.time.Instant;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InboxArchiveProjection {

	private String id;

	private String userId;

	private String title;

	private String subTitle;

	private String content;

	private InboxType inboxType;

	private String action;

	private ActionType actionType;

	private Boolean isRead;

	private Instant createdDate;

	private String pushNotificationId;

	private Map<String, String> additionalData;

	private String externalInboxId;

	private Instant externalInboxCreatedDate;

}
