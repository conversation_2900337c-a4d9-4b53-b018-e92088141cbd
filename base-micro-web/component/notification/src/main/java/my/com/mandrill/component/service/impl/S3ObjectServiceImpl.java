package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.service.S3ObjectService;
import my.com.mandrill.utilities.feign.dto.model.FileS3ObjectDTO;
import my.com.mandrill.utilities.storage.configuration.FileManagerConfiguration;
import my.com.mandrill.utilities.storage.configuration.FileStoragePropertiesConfiguration;
import my.com.mandrill.utilities.storage.model.OutputContentFile;
import my.com.mandrill.utilities.storage.service.FileManager;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class S3ObjectServiceImpl implements S3ObjectService {

	private final FileStoragePropertiesConfiguration fileStoragePropertiesConfiguration;

	private final FileManagerConfiguration fileManagerConfiguration;

	@Override
	public File getFile(FileS3ObjectDTO object) {
		try {
			FileManager fileManager = fileManagerConfiguration
					.aws(fileStoragePropertiesConfiguration.privateStorageProperty());
			OutputContentFile out = fileManager.getFile(object.getBase(), object.getKey(), object.getFilename());
			File tempFile = File.createTempFile("tmp_file", object.getFilename());
			try (FileOutputStream fos = new FileOutputStream(tempFile)) {
				out.getFile().writeTo(fos);
			}
			return tempFile;
		}
		catch (Exception e) {
			log.error("error when get-file: {}", e.getMessage());
			return null;
		}
	}

	@Override
	public void storeFile(String base, String key, File file) {
		try {
			FileManager fileManager = fileManagerConfiguration
					.aws(fileStoragePropertiesConfiguration.privateStorageProperty());
			fileManager.addFile(base + fileManager.pathSeparator(), key, file);
		}
		catch (Exception e) {
			log.error("error when store-file: {}", e.getMessage());
		}
	}

}
