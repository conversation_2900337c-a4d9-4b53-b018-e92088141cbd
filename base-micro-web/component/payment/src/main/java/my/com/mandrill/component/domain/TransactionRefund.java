package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.Status;
import org.hibernate.Hibernate;

import java.time.Instant;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "transaction_refund", uniqueConstraints = { @UniqueConstraint(columnNames = { "transaction_id" }) })
public class TransactionRefund extends AuditSection {

	@NotNull
	@OneToOne(optional = false, orphanRemoval = true)
	@JoinColumn(name = "transaction_id", nullable = false)
	private Transaction transaction;

	@NotBlank
	@Size(max = 255)
	@Column(name = "version", nullable = false)
	private String version;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "status", nullable = false)
	private Status status;

	@NotBlank
	@Size(max = 255)
	@Column(name = "refund_status", nullable = false)
	private String refundStatus;

	@Size(max = 255)
	@Column(name = "response_description")
	private String responseDescription;

	@Column(name = "date_completed")
	private Instant dateCompleted;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		TransactionRefund that = (TransactionRefund) o;
		return getId() != null && Objects.equals(getId(), that.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}