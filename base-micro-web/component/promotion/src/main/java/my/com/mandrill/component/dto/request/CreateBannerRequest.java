package my.com.mandrill.component.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
public class CreateBannerRequest implements Serializable {

	@NotBlank
	@Size(max = 255)
	private String code;

	@NotBlank
	@Size(max = 255)
	private String titleEn;

	@NotBlank
	@Size(max = 255)
	private String titleMy;

	@NotBlank
	@Size(max = 255)
	private String titleCn;

	@NotBlank
	@Size(max = 255)
	private String descriptionEn;

	@NotBlank
	@Size(max = 255)
	private String descriptionMy;

	@NotBlank
	@Size(max = 255)
	private String descriptionCn;

	@NotNull
	private Boolean active;

	@NotNull
	private Instant startDate;

	@NotNull
	private Instant endDate;

	@NotNull
	@Valid
	private BannerAttachmentRequest imageEn;

	@NotNull
	@Valid
	private BannerAttachmentRequest imageMy;

	@NotNull
	@Valid
	private BannerAttachmentRequest imageCn;

}
