package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.ConversionStatusEnum;
import my.com.mandrill.component.domain.ReferralActivity;
import my.com.mandrill.component.dto.request.ReferralActivityRequest;

import java.time.LocalDate;
import java.time.ZoneId;

public interface ReferralActivityIntegrationService {

	ReferralActivity createReferralActivity(ReferralActivityRequest referralActivityRequest, String userId,
			boolean referralTaskCompleted, ConversionStatusEnum conversionStatus);

	ReferralActivity updateReferralActivity(String userId, ConversionStatusEnum conversionStatus);

	long countByConversionStatus(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId, ConversionStatusEnum status);

}
