package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Platform;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.PlatformIntegrationService;
import my.com.mandrill.component.service.PlatformService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformIntegrationServiceImpl implements PlatformIntegrationService {

	private final PlatformService platformService;

	@Override
	public Platform create(Platform platform) {
		existByName(platform.getName());
		existByCode(platform.getCode());
		return platform;
	}

	@Override
	public Platform update(Platform platform) {
		Platform existPlatform = platformService.findById(platform.getId());

		if (!existPlatform.getCode().equalsIgnoreCase(platform.getCode())) {
			existByCode(platform.getCode());
		}

		if (!existPlatform.getName().equalsIgnoreCase(platform.getName())) {
			existByName(platform.getName());
		}

		existPlatform.setCode(platform.getCode());
		existPlatform.setName(platform.getName());
		existPlatform.setDescription(platform.getDescription());
		return existPlatform;
	}

	@Override
	public void existByCode(String code) {
		if (platformService.existsAllByCodeIgnoreCase(code)) {
			throw new BusinessException(ErrorCodeEnum.PLATFORM_BY_CODE_IS_EXIST);
		}
	}

	@Override
	public void existByName(String name) {
		if (platformService.existsAllByNameIgnoreCase(name)) {
			throw new BusinessException(ErrorCodeEnum.PLATFORM_IS_EXIST_BY_NAME);
		}
	}

}
