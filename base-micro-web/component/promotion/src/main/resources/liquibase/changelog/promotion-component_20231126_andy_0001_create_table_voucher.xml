<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="promotion-component_20231106_andy_0001" author="Andy">

        <createTable tableName="voucher_header">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_voucher_header"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="institution_id" type="VARCHAR(36)"/>
            <column name="category" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="locations" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_datetime" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="end_datetime" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="active" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(8,2)">
                <constraints nullable="false"/>
            </column>
            <column name="term_and_condition" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="voucher_detail">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_voucher_detail"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_ref_no" type="VARCHAR(36)"/>
            <column name="code" type="VARCHAR(15)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reserved_datetime" type="DATETIME"/>
            <column name="redeemed_datetime" type="DATETIME"/>
            <column name="voucher_header_id" type="VARCHAR(36)"/>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="voucher_header_id" baseTableName="voucher_detail" constraintName="FK_VOUCHER_DETAIL_ON_VOUCHER_HEADER" referencedColumnNames="id" referencedTableName="voucher_header"/>
    </changeSet>
</databaseChangeLog>