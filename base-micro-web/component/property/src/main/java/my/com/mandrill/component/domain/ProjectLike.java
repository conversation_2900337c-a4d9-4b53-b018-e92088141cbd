package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "project_likes")
public class ProjectLike extends AuditSection {

	@Column(name = "user_id")
	private String userId;

	@Column(name = "project_id")
	private String projectId;

}
