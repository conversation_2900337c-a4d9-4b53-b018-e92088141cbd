package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Developer;
import my.com.mandrill.component.dto.model.DeveloperDTO;
import my.com.mandrill.component.dto.model.DeveloperSelectViewProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface DeveloperService {

	Developer save(Developer developer);

	boolean existById(String id);

	Developer findById(String id);

	Page<DeveloperDTO> findAllPaginated(Pageable pageable, String name);

	List<DeveloperSelectViewProjection> findSelect();

	List<Developer> findByNames(Collection<String> names);

	Optional<Developer> findByName(String name);

	List<DeveloperSelectViewProjection> findSelectByIds(Collection<String> developerIds);

}
