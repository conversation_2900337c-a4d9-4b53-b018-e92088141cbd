package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.ProjectImage;

import java.util.List;
import java.util.Set;

public interface ProjectImageService {

	List<ProjectImage> save(List<ProjectImage> images);

	void deleteInProjectExcept(String projectId, Set<String> ids);

	void deleteInProjectUnitExcept(String unitId, Set<String> ids);

	List<ProjectImage> findByProject(List<String> projectId);

	List<ProjectImage> findByProjectUnit(String unitId);

}
