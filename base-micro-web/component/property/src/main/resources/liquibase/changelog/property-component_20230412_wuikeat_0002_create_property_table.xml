<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">
    <changeSet id="property-component_20230412_wuikeat_0002" author="wuikeat">

        <createTable tableName="property">
            <column name="id" type="VARCHAR(36)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_property"/>
            </column>
            <column defaultValueComputed="NOW()" name="created_date" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="sys-admin" name="created_by" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="NOW()" name="last_modified_date" type="DATETIME"/>
            <column name="last_modified_by" type="VARCHAR(100)"/>
            <column name="user_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="property_type_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="property_sub_type_id" type="VARCHAR(36)"/>
            <column name="property_sub_type_other" type="VARCHAR(200)"/>
            <column name="purchase_value" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="purchase_status" type="VARCHAR(50)"/>
            <column name="loan_provider_id" type="VARCHAR(36)"/>
            <column name="loan_duration" type="INT"/>
            <column name="loan_amount" type="DECIMAL(15, 2)"/>
            <column name="loan_percentage" type="DECIMAL(15, 2)"/>
            <column name="repayment_start_year" type="INT"/>
            <column name="monthly_installment" type="DECIMAL(15, 2)"/>
            <column name="interest_rate" type="DECIMAL(15, 2)"/>
            <column name="is_rented" type="BOOLEAN"/>
            <column name="rental_amount" type="DECIMAL(15, 2)"/>
            <column name="installment_reminder" type="BOOLEAN"/>
            <column name="rental_reminder" type="BOOLEAN"/>
            <column name="monthly_reminder" type="INT"/>
            <column name="current_residence" type="BOOLEAN"/>
            <column name="address1" type="VARCHAR(100)"/>
            <column name="address2" type="VARCHAR(100)"/>
            <column name="address3" type="VARCHAR(100)"/>
            <column name="label" type="VARCHAR(200)"/>
            <column name="active" type="BOOLEAN"/>
            <column name="attachment_group_id" type="VARCHAR(36)"/>
        </createTable>
        <addForeignKeyConstraint baseColumnNames="property_sub_type_id" baseTableName="property" constraintName="FK_PROPERTY_ON_PROPERTY_SUB_TYPE" referencedColumnNames="id" referencedTableName="property_sub_type"/>
        <addForeignKeyConstraint baseColumnNames="property_type_id" baseTableName="property" constraintName="FK_PROPERTY_ON_PROPERTY_TYPE" referencedColumnNames="id" referencedTableName="property_type"/>
    </changeSet>
</databaseChangeLog>