package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;

@SpringBootTest(classes = DashboardTriggerServiceImpl.class)
public class DashboardTriggerServiceImplTest {

	@Autowired
	private DashboardTriggerServiceImpl dashboardTriggerService;

	@MockBean
	private KafkaTemplate<String, String> kafkaTemplate;

	@MockBean
	private ObjectMapper objectMapper;

	@Test
	public void testSendSuccess() throws Exception {
		Mockito.when(objectMapper.writeValueAsString(Mockito.any(DashboardActivityMessage.class))).thenReturn("""
				{"category": "APP_ACTIVE_USER"}
				""");

		dashboardTriggerService
				.send(DashboardActivityMessage.builder().category(DashboardCategory.APP_ACTIVE_USER).build());
		Mockito.verify(objectMapper, Mockito.times(1)).writeValueAsString(Mockito.any(DashboardActivityMessage.class));
		Mockito.verify(kafkaTemplate, Mockito.times(1)).send(Mockito.eq(KafkaTopic.DASHBOARD_ACTIVITY),
				Mockito.anyString());
	}

	@Test
	public void testSendErrorWriter() throws Exception {
		Mockito.when(objectMapper.writeValueAsString(Mockito.any(DashboardActivityMessage.class)))
				.thenThrow(new JsonProcessingException("error parse") {
				});

		dashboardTriggerService
				.send(DashboardActivityMessage.builder().category(DashboardCategory.APP_ACTIVE_USER).build());
		Mockito.verify(objectMapper, Mockito.times(1)).writeValueAsString(Mockito.any(DashboardActivityMessage.class));
	}

}
