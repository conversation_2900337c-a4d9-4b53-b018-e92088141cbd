package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;

@Getter
@Setter
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "retirement_provider")
public class RetirementProvider extends AuditSection {

	@NotBlank
	@Size(max = 255)
	@Column(name = "full_name", nullable = false)
	private String fullName;

	@NotBlank
	@Size(max = 255)
	@Column(name = "code", nullable = false)
	private String code;

	@ToString.Exclude
	@ManyToOne(optional = false)
	@JoinColumn(name = "account_type")
	private RetirementAccountType accountType;

	@Column(name = "image")
	private String image;

	@Size(max = 36)
	@Column(name = "attachment_group_id", length = 36)
	private String attachmentGroupId;

	@NotNull
	@Column(name = "active", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	@Builder.Default
	private Boolean isActive = true;

	@Column(name = "description")
	private String description;

	@Size(max = 7)
	@Column(name = "color_code", length = 7)
	private String colorCode;

	@NotNull
	@Column(name = "is_partner", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean isPartner;

}
