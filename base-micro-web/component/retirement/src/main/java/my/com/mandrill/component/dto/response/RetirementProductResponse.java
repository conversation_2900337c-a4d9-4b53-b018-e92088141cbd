package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetirementProductResponse {

	private String id;

	private String name;

	private String description;

	private String logoUrl;

	private BigDecimal interestRate;

	private String screenRoute;

}
