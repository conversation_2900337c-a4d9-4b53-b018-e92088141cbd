package my.com.mandrill.component.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@AllArgsConstructor
@Getter
enum ErrorCodeEnum {

	UNKNOWN_EXCEPTION("SCH9999", "This is unexpected error. Contact <EMAIL>",
			HttpStatus.INTERNAL_SERVER_ERROR),

	DESTINATION_NOT_FOUND_EXCEPTION("SCH0001", "This is unexpected error. Contact <EMAIL>",
			HttpStatus.BAD_REQUEST),
	MISSING_CRON_EXPRESSION_EXCEPTION("SCH0002", "This is unexpected error. Contact <EMAIL>",
			HttpStatus.BAD_REQUEST),
	MISSING_SCHEDULED_TIME_EXCEPTION("SCH0003", "This is unexpected error. Contact <EMAIL>",
			HttpStatus.BAD_REQUEST),
	TOPIC_NOT_FOUND_EXCEPTION("SCH0004", "This is unexpected error. Contact <EMAIL>",
			HttpStatus.BAD_REQUEST),;

	private final String code;

	private final String description;

	private final HttpStatus httpStatus;

}