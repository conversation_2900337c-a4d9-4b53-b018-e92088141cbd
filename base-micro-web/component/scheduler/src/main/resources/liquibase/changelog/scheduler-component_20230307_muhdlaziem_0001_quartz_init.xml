<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <property name="table_prefix" value="QRTZ_"/>

    <property name="blob_type" value="BYTEA" dbms="postgresql"/>
    <property name="blob_type" value="BLOB"/>

    <changeSet id="scheduler-component_20230307_muhdlaziem_0001" author="muhdlaziem">

        <createTable tableName="${table_prefix}LOCKS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="LOCK_NAME" type="VARCHAR(40)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, LOCK_NAME" tableName="${table_prefix}LOCKS"/>

        <createTable tableName="${table_prefix}FIRED_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="ENTRY_ID" type="VARCHAR(95)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="INSTANCE_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="FIRED_TIME" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="SCHED_TIME" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="PRIORITY" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="STATE" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)"/>
            <column name="JOB_GROUP" type="VARCHAR(200)"/>
            <column name="IS_NONCONCURRENT" type="BOOLEAN"/>
            <column name="REQUESTS_RECOVERY" type="BOOLEAN"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, ENTRY_ID" tableName="${table_prefix}FIRED_TRIGGERS"/>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_INST_JOB_REQ_RCVRY">
            <column name="SCHED_NAME"/>
            <column name="INSTANCE_NAME"/>
            <column name="REQUESTS_RECOVERY"/>
        </createIndex>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_J_G">
            <column name="SCHED_NAME"/>
            <column name="JOB_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_JG">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_T_G">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_TG">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}FIRED_TRIGGERS" indexName="IDX_${table_prefix}FT_TRIG_INST_NAME">
            <column name="SCHED_NAME"/>
            <column name="INSTANCE_NAME"/>
        </createIndex>

        <createTable tableName="${table_prefix}CALENDARS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="CALENDAR_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="CALENDAR" type="${blob_type}">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, CALENDAR_NAME" tableName="${table_prefix}CALENDARS"/>

        <createTable tableName="${table_prefix}PAUSED_TRIGGER_GRPS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_GROUP" tableName="${table_prefix}PAUSED_TRIGGER_GRPS"/>

        <createTable tableName="${table_prefix}SCHEDULER_STATE">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="INSTANCE_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="LAST_CHECKIN_TIME" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="CHECKIN_INTERVAL" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, INSTANCE_NAME" tableName="${table_prefix}SCHEDULER_STATE"/>

        <createTable tableName="${table_prefix}JOB_DETAILS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(250)"/>
            <column name="JOB_CLASS_NAME" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="IS_DURABLE" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="IS_NONCONCURRENT" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="IS_UPDATE_DATA" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="REQUESTS_RECOVERY" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_DATA" type="${blob_type}"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, JOB_NAME, JOB_GROUP" tableName="${table_prefix}JOB_DETAILS"/>

        <createIndex tableName="${table_prefix}JOB_DETAILS" indexName="IDX_${table_prefix}J_GRP">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}JOB_DETAILS" indexName="IDX_${table_prefix}J_REQ_RECOVERY">
            <column name="SCHED_NAME"/>
            <column name="REQUESTS_RECOVERY"/>
        </createIndex>

        <createTable tableName="${table_prefix}TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(250)"/>
            <column name="NEXT_FIRE_TIME" type="BIGINT"/>
            <column name="PREV_FIRE_TIME" type="BIGINT"/>
            <column name="PRIORITY" type="INTEGER"/>
            <column name="TRIGGER_STATE" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_TYPE" type="VARCHAR(8)">
                <constraints nullable="false"/>
            </column>
            <column name="START_TIME" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="END_TIME" type="BIGINT"/>
            <column name="CALENDAR_NAME" type="VARCHAR(200)"/>
            <column name="MISFIRE_INSTR" type="smallint"/>
            <column name="JOB_DATA" type="${blob_type}"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP" tableName="${table_prefix}TRIGGERS"/>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_C">
            <column name="SCHED_NAME"/>
            <column name="CALENDAR_NAME"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_G">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_JG">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_N_G_STATE">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_N_STATE">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_NAME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_NEXT_FIRE_TIME">
            <column name="SCHED_NAME"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_NFT_MISFIRE">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_NFT_ST">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_STATE"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_NFT_ST_MISFIRE">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_NFT_ST_MISFIRE_GRP">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>

        <createIndex tableName="${table_prefix}TRIGGERS" indexName="IDX_${table_prefix}T_STATE">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>

        <createTable tableName="${table_prefix}BLOB_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="BLOB_DATA" type="${blob_type}"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP" tableName="${table_prefix}BLOB_TRIGGERS"/>

        <createTable tableName="${table_prefix}SIMPROP_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="STR_PROP_1" type="VARCHAR(512)"/>
            <column name="STR_PROP_2" type="VARCHAR(512)"/>
            <column name="STR_PROP_3" type="VARCHAR(512)"/>
            <column name="INT_PROP_1" type="INTEGER"/>
            <column name="INT_PROP_2" type="INTEGER"/>
            <column name="LONG_PROP_1" type="BIGINT"/>
            <column name="LONG_PROP_2" type="BIGINT"/>
            <column name="DEC_PROP_1" type="NUMERIC(13,4)"/>
            <column name="DEC_PROP_2" type="NUMERIC(13,4)"/>
            <column name="BOOL_PROP_1" type="BOOLEAN"/>
            <column name="BOOL_PROP_2" type="BOOLEAN"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                       tableName="${table_prefix}SIMPROP_TRIGGERS"/>

        <createTable tableName="${table_prefix}CRON_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="CRON_EXPRESSION" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TIME_ZONE_ID" type="VARCHAR(80)"/>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP" tableName="${table_prefix}CRON_TRIGGERS"/>

        <createTable tableName="${table_prefix}SIMPLE_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="REPEAT_COUNT" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="REPEAT_INTERVAL" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="TIMES_TRIGGERED" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey columnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                       tableName="${table_prefix}SIMPLE_TRIGGERS"/>

        <addForeignKeyConstraint baseTableName="${table_prefix}TRIGGERS"
                                 constraintName="${table_prefix}TRIGGERS_SCHED_NAME_FKEY"
                                 baseColumnNames="SCHED_NAME, JOB_NAME, JOB_GROUP"
                                 referencedTableName="${table_prefix}JOB_DETAILS"
                                 referencedColumnNames="SCHED_NAME, JOB_NAME, JOB_GROUP"/>

        <addForeignKeyConstraint baseTableName="${table_prefix}SIMPLE_TRIGGERS"
                                 constraintName="${table_prefix}SIMPLE_TRIGGERS_SCHED_NAME_FKEY"
                                 baseColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                                 referencedTableName="${table_prefix}TRIGGERS"
                                 referencedColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"/>

        <addForeignKeyConstraint baseTableName="${table_prefix}CRON_TRIGGERS"
                                 constraintName="${table_prefix}CRON_TRIGGERS_SCHED_NAME_FKEY"
                                 baseColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                                 referencedTableName="${table_prefix}TRIGGERS"
                                 referencedColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"/>

        <addForeignKeyConstraint baseTableName="${table_prefix}SIMPROP_TRIGGERS"
                                 constraintName="${table_prefix}SIMPROP_TRIGGERS_SCHED_NAME_FKEY"
                                 baseColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                                 referencedTableName="${table_prefix}TRIGGERS"
                                 referencedColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"/>

        <addForeignKeyConstraint baseTableName="${table_prefix}BLOB_TRIGGERS"
                                 constraintName="${table_prefix}BLOB_TRIGGERS_SCHED_NAME_FKEY"
                                 baseColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"
                                 referencedTableName="${table_prefix}TRIGGERS"
                                 referencedColumnNames="SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP"/>
    </changeSet>
</databaseChangeLog>