package my.com.mandrill.component.config;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class JacksonConfig {

	public static final CsvMapper CSV_MAPPER = csvMapper();

	private static CsvMapper csvMapper() {
		CsvMapper csvMapper = new CsvMapper();

		csvMapper.findAndRegisterModules();

		// CsvMapper config
		csvMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
		csvMapper.enable(CsvParser.Feature.TRIM_SPACES);

		return csvMapper;
	}

}
