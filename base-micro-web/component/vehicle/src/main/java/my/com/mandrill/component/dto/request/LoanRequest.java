package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoanRequest implements Serializable {

	@NotNull
	private ObjectRequest provider;

	@NotNull
	@Min(1)
	@Max(9)
	private Short duration;

	@NotNull
	private Month repaymentStartMonth;

	@NotNull
	@Schema(implementation = Short.class, example = "1997")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "yyyy")
	private Year repaymentStartYear;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	private BigDecimal monthlyInstallment;

	@Digits(integer = 15, fraction = 2)
	private BigDecimal amount;

	@Digits(integer = 5, fraction = 2)
	private BigDecimal interestRate;

}
