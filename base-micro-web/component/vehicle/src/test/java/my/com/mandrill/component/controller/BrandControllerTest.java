package my.com.mandrill.component.controller;

import my.com.mandrill.component.domain.Brand;
import my.com.mandrill.component.domain.VehicleType;
import my.com.mandrill.component.service.impl.BrandServiceImpl;
import my.com.mandrill.component.service.impl.VehicleTypeServiceImpl;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(value = BrandController.class, excludeAutoConfiguration = SecurityAutoConfiguration.class)
@AutoConfigureMockMvc
@ContextConfiguration(classes = BrandController.class)
class BrandControllerTest {

	@Autowired
	MockMvc mockMvc;

	@MockBean
	BrandServiceImpl brandService;

	@MockBean
	VehicleTypeServiceImpl vehicleTypeService;

	Brand brandMock;

	List<Brand> brandListMock;

	@Test
	void findAll_GiveValidArgument_Positive() throws Exception {
		brandMock = new Brand();
		brandListMock = List.of(brandMock);
		Mockito.when(vehicleTypeService.findById(any())).thenReturn(new VehicleType());
		Mockito.when(brandService.findByVehicleType(any(), any())).thenReturn(brandListMock);
		mockMvc.perform(MockMvcRequestBuilders.get("/brands").param("vehicleTypeId", "1")).andDo(print())
				.andExpect(status().isOk()).andExpect(jsonPath("$.size()").value(1));

	}

}
