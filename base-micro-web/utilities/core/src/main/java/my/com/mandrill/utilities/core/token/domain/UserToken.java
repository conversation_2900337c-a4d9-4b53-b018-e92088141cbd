package my.com.mandrill.utilities.core.token.domain;

import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.TimeToLive;
import org.springframework.data.redis.core.index.Indexed;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@Builder
@Validated
@RedisHash("{my.com.mandrill.utilities.core.token.domain.Token}")
@NoArgsConstructor
@AllArgsConstructor
public class UserToken implements Serializable {

	@Id
	@Indexed
	private String id;

	private String userId;

	private String userRefNo;

	private AccessTypeEnum accessType;

	private LoginTypeEnum loginType;

	@Builder.Default
	private Set<String> permissions = new HashSet<>();

	@TimeToLive
	private Long expiration;

}
