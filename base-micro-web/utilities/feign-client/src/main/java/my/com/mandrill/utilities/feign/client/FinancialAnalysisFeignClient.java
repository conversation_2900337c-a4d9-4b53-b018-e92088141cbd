package my.com.mandrill.utilities.feign.client;

import my.com.mandrill.utilities.feign.dto.JobStatusDTO;
import my.com.mandrill.utilities.feign.dto.request.JobStatusRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.Instant;

@FeignClient("financial-analysis-component")
public interface FinancialAnalysisFeignClient {

	@PostMapping("/jobs/integration/status")
	JobStatusDTO processIntegrationJobStatus(@RequestBody JobStatusRequest jobStatusRequest);

	@GetMapping("/financial-analyses/count/completed")
	Long countCompleted();

	@GetMapping("/jobs-statements/completed-count")
	long countCompletedStatements(@RequestParam Instant startDate, @RequestParam Instant endDate);

	@GetMapping("/user-average/integration/new-user-average-count")
	long countNewUserAverage(@RequestParam Instant startDate, @RequestParam Instant endDate);

}
