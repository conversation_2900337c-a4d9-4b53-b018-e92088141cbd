package my.com.mandrill.utilities.feign.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RefinanceProductResponse {

	private List<HomeLoanItem> items;

	private int page;

	@JsonProperty("per_page")
	private int perPage;

	@JsonProperty("refinance_eligibility")
	private boolean refinanceEligibility;

	@JsonProperty("total_count")
	private int totalCount;

	@Data
	public static class HomeLoanItem {

		@JsonProperty("apply_link")
		private String applyLink;

		@JsonProperty("card_type")
		private String cardType;

		private Map<String, Object> data;

		private String description;

		private String entity;

		private Highlights highlights;

		@JsonProperty("issuer_code")
		private String issuerCode;

		@JsonProperty("issuer_type")
		private String issuerType;

		private String logo;

		private String name;

		@JsonProperty("product_id")
		private String productId;

		@JsonProperty("product_link")
		private String productLink;

		private String type;

	}

	@Data
	public static class Highlights {

		private List<String> additional;

		@JsonProperty("default")
		private List<String> defaultHighlights;

		private List<String> suggested;

	}

}
