package my.com.mandrill.utilities.general.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPublicWebResponse implements Serializable {

	private String id;

	private String fullName;

	private String email;

	private String phoneNumber;

	private String phoneCountry;

	private String nric;

	private String userRefNo;

	private boolean existingUser;

}
