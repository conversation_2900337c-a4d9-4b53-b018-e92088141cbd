package my.com.mandrill.utilities.general.exception;

import jakarta.persistence.EntityNotFoundException;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import org.springframework.security.access.AccessDeniedException;

import java.util.function.Supplier;

public final class ExceptionPredicate {

	private static final String NOT_SUPPORTED_MESSAGE_FORMAT = "%s not supported";

	private ExceptionPredicate() {
		throw new IllegalStateException("Utility class");
	}

	public static Supplier<EntityNotFoundException> institutionNotFound(String institutionId) {
		return () -> new EntityNotFoundException(String.format("Institution with id %s was not found", institutionId));
	}

	public static Supplier<EntityNotFoundException> currentUserAndInstitutionNotMatch(String username,
			String institutionId) {
		return () -> new EntityNotFoundException(
				String.format("Institution with id %s and username %s was not match", institutionId, username));
	}

	public static Supplier<EntityNotFoundException> sysConfigByIdNotFound(String systemConfigurationId) {
		return () -> new EntityNotFoundException(
				String.format("System Configuration with id=%s Not Found", systemConfigurationId));
	}

	public static Supplier<EntityNotFoundException> sysConfigByInstitutionIdAndCodeNotFound(String institutionId,
			String code) {
		return () -> new EntityNotFoundException(
				String.format("System Configuration with institutionId=%s and code=%s Not Found", institutionId, code));
	}

	public static Supplier<EntityNotFoundException> permissionByIdNotFound(String permissionId) {
		return () -> new EntityNotFoundException(String.format("Permission with id=%s Not Found", permissionId));
	}

	public static Supplier<EntityNotFoundException> authorityByIdNotFound(String authorityId) {
		return () -> new EntityNotFoundException(String.format("Authority with id=%s Not Found", authorityId));
	}

	public static Supplier<EntityNotFoundException> authorityByIdAndInstitutionIdNotFound(String authorityId,
			String institutionId) {
		return () -> new EntityNotFoundException(
				String.format("Authority with id=%s and Institution with id=%s Not Found", authorityId, institutionId));
	}

	public static Supplier<EntityNotFoundException> businessNatureNotFound(String businessNatureId) {
		return () -> new EntityNotFoundException(
				String.format("BusinessNature with id=%s Not Found", businessNatureId));
	}

	public static Supplier<EntityNotFoundException> countryNotFound(String countryId) {
		return () -> new EntityNotFoundException(String.format("Country with id=%s Not Found", countryId));
	}

	public static Supplier<EntityNotFoundException> countryByNationalityIdNotFound(String nationalityId) {
		return () -> new EntityNotFoundException(
				String.format("Country with nationality id=%s Not Found", nationalityId));
	}

	public static Supplier<EntityNotFoundException> currencyNotFound(String currencyId) {
		return () -> new EntityNotFoundException(String.format("Currency with id=%s Not Found", currencyId));
	}

	public static Supplier<EntityNotFoundException> educationLevelNotFound(String educationLevelId) {
		return () -> new EntityNotFoundException(
				String.format("EducationLevel with id=%s Not Found", educationLevelId));
	}

	public static Supplier<EntityNotFoundException> employmentTypeNotFound(String employmentTypeId) {
		return () -> new EntityNotFoundException(
				String.format("EmploymentType with id=%s Not Found", employmentTypeId));
	}

	public static Supplier<EntityNotFoundException> employmentTypeAndLoanLimitTrueNotFound(String employmentTypeId) {
		return () -> new EntityNotFoundException(
				String.format("EmploymentType with id=%s and loan limit is true Not Found", employmentTypeId));
	}

	public static Supplier<EntityNotFoundException> nationalityNotFound(String nationalityId) {
		return () -> new EntityNotFoundException(String.format("Nationality with id=%s Not Found", nationalityId));
	}

	public static Supplier<EntityNotFoundException> occupationGroupNotFound(String occupationGroupId) {
		return () -> new EntityNotFoundException(
				String.format("OccupationGroup with id=%s Not Found", occupationGroupId));
	}

	public static Supplier<EntityNotFoundException> stateNotFound(String stateId) {
		return () -> new EntityNotFoundException(String.format("State with id=%s Not Found", stateId));
	}

	public static Supplier<EntityNotFoundException> interestNotFound(String interestId) {
		return () -> new EntityNotFoundException(String.format("Interest with id=%s Not Found", interestId));
	}

	public static Supplier<EntityNotFoundException> financialGoalNotFound(String financialGoalId) {
		return () -> new EntityNotFoundException(String.format("Financial Goal with id=%s Not Found", financialGoalId));
	}

	public static Supplier<EntityNotFoundException> domainNotFound(String domainId) {
		return () -> new EntityNotFoundException(String.format("Domain with id=%s Not Found", domainId));
	}

	public static Supplier<EntityNotFoundException> domainNotFound(String code, String name) {
		return () -> new EntityNotFoundException(
				String.format("Domain with code=%s and name=%s Not Found", code, name));
	}

	public static Supplier<EntityNotFoundException> userNotFound(String username, LoginTypeEnum loginType) {
		return () -> new EntityNotFoundException(
				String.format("User with username=%s and loginType=%s Not Found", username, loginType));
	}

	public static Supplier<EntityNotFoundException> userNotFoundByRefNo(String refNo) {
		return () -> new EntityNotFoundException(String.format("User with username=%s Not Found", refNo));
	}

	public static Supplier<EntityNotFoundException> userNotFoundByPhoneNumber(String refNo) {
		return () -> new EntityNotFoundException(
				String.format("User with phoneNumber=%s and loginType=%s Not Found", refNo));
	}

	public static Supplier<EntityNotFoundException> emailNotFound(String email, LoginTypeEnum loginType) {
		return () -> new EntityNotFoundException(
				String.format("User with email=%s and loginType=%s Not Found", email, loginType));
	}

	public static Supplier<EntityNotFoundException> incomeTypeNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Income Type with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> sysConfigNotFound(String code) {
		return () -> new EntityNotFoundException(
				String.format("System Configuration with code: %s was not found", code));
	}

	public static Supplier<EntityNotFoundException> segmentNotFound(String segmentId) {
		return () -> new EntityNotFoundException(String.format("Segment with id=%s Not Found", segmentId));
	}

	public static Supplier<AccessDeniedException> cantAccessUser() {
		return () -> new AccessDeniedException("System can't load user data");
	}

	public static Supplier<EntityNotFoundException> expenseTypeNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Expense Type with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> authorityNotFoundByName(String name) {
		return () -> new EntityNotFoundException(String.format("Authority with name=%s Not Found", name));

	}

	public static Supplier<EntityNotFoundException> userNotFoundByUsernameIgnoreCaseAndLoginType(String username,
			LoginTypeEnum loginType) {
		return () -> new EntityNotFoundException(
				String.format("User with username=%s and loginType=%s Not Found", username, loginType));
	}

	public static Supplier<EntityNotFoundException> bankNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Bank with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> bankDetailNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Bank Detail with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> bankListNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Bank List with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> bankListAiMappingNotFound(String aiMapping) {
		return () -> new EntityNotFoundException(String.format("Provider with name: %s not found", aiMapping));
	}

	public static Supplier<EntityNotFoundException> bankListInstitutionMappingNotFound(String aiMapping) {
		return () -> new EntityNotFoundException(String.format("Provider with institution: %s not found", aiMapping));
	}

	public static Supplier<EntityNotFoundException> bankAccountTypeNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Bank Account Type with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> propertyTypeNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Property Type with Id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> propertySubTypeNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Property Sub Type with Id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> propertyNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Property with Id=%s Not Found", id));
	}

	public static Supplier<GlobalNotSupportedException> notSupportedByEntityName(String entityName) {
		return () -> new GlobalNotSupportedException(String.format(NOT_SUPPORTED_MESSAGE_FORMAT, entityName));
	}

	public static Supplier<EntityNotFoundException> utilityNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Utility with Id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> bankDetailNotFoundByAttachmentGroupId(String id) {
		return () -> new EntityNotFoundException(
				String.format("Bank Detail with attachment group id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> dashboardTypeNotFound(String description) {
		return () -> new EntityNotFoundException(
				"Dashboard Type with description %s was not found".formatted(description));
	}

	public static Supplier<EntityNotFoundException> institutionNotFoundByAiMapping(String aiMapping) {
		return () -> new EntityNotFoundException(String.format("Not Registered Institution: %s", aiMapping));
	}

	public static Supplier<EntityNotFoundException> userNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("User with id %s was not found", id));
	}

	public static Supplier<EntityNotFoundException> globalSysConfigByCodeNotFound(String code) {
		return () -> new EntityNotFoundException(
				String.format("Global System Configuration with code=%s Not Found", code));
	}

	public static Supplier<EntityNotFoundException> bankListByIssuerCodeNotFound(String issuerCode) {
		return () -> new EntityNotFoundException(String.format("Bank List with issuerCode=%s Not Found", issuerCode));
	}

	public static Supplier<EntityNotFoundException> ekycNotFoundByApplicantId(String applicantId) {
		return () -> new EntityNotFoundException(String.format("EKYC with applicantId %s was not found", applicantId));
	}

	public static Supplier<EntityNotFoundException> incomeTypeNotFoundByCode(String code) {
		return () -> new EntityNotFoundException(String.format("Income Type with code=%s Not Found", code));
	}

	public static Supplier<EntityNotFoundException> businessNatureNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Business Nature with id=%s was not found", id));
	}

	public static Supplier<EntityNotFoundException> providerInsuranceNotFoundByFinologyCode(String code) {
		return () -> new EntityNotFoundException(String.format("Provider Insurance with code: %s was not found", code));
	}

	public static Supplier<EntityNotFoundException> extraCoverageCodeNotFoundByCode(String code) {
		return () -> new EntityNotFoundException(String.format("Extra coverage with code: %s was not found", code));
	}

	public static Supplier<EntityNotFoundException> accessNotFoundById(String id) {
		return () -> new EntityNotFoundException(String.format("Access with id: %s was not found", id));
	}

	public static Supplier<EntityNotFoundException> surveyFormNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Survey Form with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> surveyAnswerNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Survey Answer with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> surveyQuestionNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Survey Question with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> surveyDetailNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Detail Question with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> activeFormNotFound() {
		return () -> new EntityNotFoundException("Active Form not found");
	}

	public static Supplier<EntityNotFoundException> postcodeNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Postcode with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> postcodeNotFoundByCode(String id) {
		return () -> new EntityNotFoundException(String.format("Postcode with code=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> productConfigByIdNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Product Configuration with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> productConfigByProductIdNotFound(String productId) {
		return () -> new EntityNotFoundException(
				String.format("Product Configuration with productId=%s Not Found", productId));
	}

	public static Supplier<EntityNotFoundException> productConfigTemplateByIdNotFound(String id) {
		return () -> new EntityNotFoundException(
				String.format("Product Configuration Template with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> categoryNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Category with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> propertyParameterNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Property Parameter with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> tokenNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("Token with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> userSavingGoalByIdNotFound(String savingGoalId) {
		return () -> new EntityNotFoundException(String.format("User Saving Goal with id=%s Not Found", savingGoalId));
	}

	public static Supplier<EntityNotFoundException> userInterestedRecordNotFound(String id) {
		return () -> new EntityNotFoundException(String.format("UserInterestedRedirect with id=%s Not Found", id));
	}

	public static Supplier<EntityNotFoundException> userSurveyNotFound(SurveyFormType surveyFormType) {
		return () -> new EntityNotFoundException(String.format("Survey Form with type=%s Not Found", surveyFormType));
	}

	public static Supplier<EntityNotFoundException> surveyHeaderNotFound(String surveyHeaderId) {
		return () -> new EntityNotFoundException(String.format("Survey Header with id=%s Not Found", surveyHeaderId));
	}

}
