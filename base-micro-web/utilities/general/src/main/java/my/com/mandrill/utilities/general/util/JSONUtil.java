package my.com.mandrill.utilities.general.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContextException;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;

@Component
@RequiredArgsConstructor
public class JSONUtil {

	private final ObjectMapper mapper;

	public String convertToString(Object obj) {
		try {
			mapper.registerModule(new JavaTimeModule());
			return mapper.writeValueAsString(obj);
		}
		catch (JsonProcessingException e) {
			throw new ApplicationContextException(e.getMessage(), e.getCause());
		}
	}

	public <T> T convertValueFromJson(String json, TypeReference<T> type) {
		try {
			mapper.registerModule(new JavaTimeModule());
			return mapper.readValue(json, type);
		}
		catch (IOException e) {
			throw new ApplicationContextException(e.getMessage(), e.getCause());
		}
	}

	public <T> T convertValueFromJson(File json, Class<T> type) {
		try {
			mapper.registerModule(new JavaTimeModule());
			return mapper.readValue(json, type);
		}
		catch (IOException e) {
			throw new ApplicationContextException(e.getMessage(), e.getCause());
		}
	}

	public <T> T convertValueFromJson(Object json, TypeReference<T> type) {
		mapper.registerModule(new JavaTimeModule());
		return mapper.convertValue(json, type);
	}

	public <T> T convertValue(Object from, Class<T> to) {
		mapper.registerModule(new JavaTimeModule());
		return mapper.convertValue(from, to);
	}

}
