package my.com.mandrill.utilities.general.util;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.RegexConstants;
import my.com.mandrill.utilities.general.domain.RunningNumber;
import my.com.mandrill.utilities.general.repository.RunningNumberRepository;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
public class RunningNumberUtil {

	public static final long DEFAULT_INITIAL_NEXT = 1L;

	private static final String NUMBER_YEAR_FORMAT = "yyyy";

	private static final String NUMBER_MONTH_FORMAT = "MM";

	private static final String NUMBER_DAY_FORMAT = "dd";

	private static final String TYPE_ADMIN = "0";

	private static final String TYPE_USER = "1";

	private final RunningNumberRepository runningNumberRepository;

	public RunningNumberUtil(@Autowired(required = false) RunningNumberRepository runningNumberRepository) {
		this.runningNumberRepository = runningNumberRepository;
	}

	@Transactional
	public String getLatestRunningNumber(String module, boolean isAdmin) {
		Optional<RunningNumber> runningNumberOptional = runningNumberRepository.findById(module);
		if (runningNumberOptional.isPresent()) {
			StringBuilder stringBuilder = new StringBuilder();

			RunningNumber runningNumber = runningNumberOptional.get();

			if (StringUtils.isNotBlank(runningNumber.getPrefix())) {
				stringBuilder.append(runningNumber.getPrefix());
			}

			String date = parseDate(runningNumber);
			String type = isAdmin ? TYPE_ADMIN : TYPE_USER;
			String seq = generateSeq(runningNumber);

			runningNumberRepository.save(runningNumber);

			return stringBuilder.append(date).append(type).append(seq).toString();
		}
		else
			return null;
	}

	@Transactional
	public String getLatestRunningNumber(String module) {
		Optional<RunningNumber> runningNumberOptional = runningNumberRepository.findById(module);
		if (runningNumberOptional.isPresent()) {
			StringBuilder stringBuilder = new StringBuilder();

			RunningNumber runningNumber = runningNumberOptional.get();

			if (StringUtils.isNotBlank(runningNumber.getPrefix())) {
				stringBuilder.append(runningNumber.getPrefix());
			}

			String date = parseDate(runningNumber);
			String seq = generateSeq(runningNumber);

			runningNumberRepository.save(runningNumber);

			return stringBuilder.append(date).append(seq).toString();
		}
		else
			return null;
	}

	@Transactional
	public String getLatestRunningNumberWithoutDate(String module) {
		Optional<RunningNumber> runningNumberOptional = runningNumberRepository.findById(module);
		if (runningNumberOptional.isPresent()) {
			StringBuilder stringBuilder = new StringBuilder();

			RunningNumber runningNumber = runningNumberOptional.get();

			if (StringUtils.isNotBlank(runningNumber.getPrefix())) {
				stringBuilder.append(runningNumber.getPrefix());
			}

			String seq = generateSeq(runningNumber);

			runningNumberRepository.save(runningNumber);

			return stringBuilder.append(seq).toString();
		}
		else
			return null;
	}

	@Transactional
	public void resetRunningNumber(String module) {
		Optional<RunningNumber> runningNumberOptional = runningNumberRepository.findById(module);
		if (runningNumberOptional.isPresent()) {
			RunningNumber runningNumber = runningNumberOptional.get();
			Long result = runningNumberRepository.resetSeq(module, DEFAULT_INITIAL_NEXT);
			runningNumber.setNext(result);
			log.info("Running Number [{}] reset to {}", runningNumber.getModule(), runningNumber.getNext());
		}

	}

	private String parseDate(RunningNumber runningNumber) {
		DateTime datetime = DateTime.now(DateTimeZone.forID(runningNumber.getTimeZone()));
		StringBuilder str = new StringBuilder();

		str.append(datetime.toString(NUMBER_YEAR_FORMAT)).append(datetime.toString(NUMBER_MONTH_FORMAT));
		if (Boolean.TRUE.equals(runningNumber.getIncludeDay())) {
			str.append(datetime.toString(NUMBER_DAY_FORMAT));
		}

		return str.toString();
	}

	private String generateSeq(RunningNumber runningNumber) {
		Long nextSeq = runningNumberRepository.findNextSeq(runningNumber.getModule());
		String seq = String.format(runningNumber.getFormat(), nextSeq);

		if (seq.matches(RegexConstants.ONLY_NINE)) {
			Long result = runningNumberRepository.resetSeq(runningNumber.getModule(), DEFAULT_INITIAL_NEXT);
			runningNumber.setNext(result);
		}
		else {
			runningNumber.setNext(nextSeq + DEFAULT_INITIAL_NEXT);
		}

		return seq;
	}

}
